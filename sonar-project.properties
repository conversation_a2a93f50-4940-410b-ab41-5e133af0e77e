sonar.projectName=nova-acc-offer-consumer
sonar.projectKey=nova-acc-offer-consumer
sonar.dynamicAnalysis=reuseReports
sonar.junit.reportPaths=nova-acc-offer-consumer-service/build/test-results/test
sonar.tests=nova-acc-offer-consumer-service/src/test
sonar.sources=nova-acc-offer-consumer-service/src/main
sonar.java.binaries=nova-acc-offer-consumer-service/build/classes
sonar.java.libraries=nova-acc-offer-consumer-service/build/libs/app.jar
sonar.java.test.libraries=nova-acc-offer-consumer-service/build/libs/app.jar
sonar.language=java
sonar.java.coveragePlugin=jacoco
sonar.coverage.jacoco.xmlReportPaths=nova-acc-offer-consumer-service/build/reports/jacoco/report.xml



