pluginManagement {
    apply from: "dependency.gradle"
    plugins {
        id "com.gradle.enterprise" version "${versions.gradleEnterpriseVersion}"
        id "com.jfrog.artifactory" version "${versions.jfrogVersion}"
        id "io.spring.dependency-management" version "${versions.springDependencyManagementVersion}"
        id "net.researchgate.release" version "${versions.gradleReleaseVersion}"
        id "org.jetbrains.kotlin.jvm" version "${versions.kotlinVersion}"
        id "org.jetbrains.kotlin.plugin.allope" version "${versions.kotlinVersion}"
        id "org.jetbrains.kotlin.plugin.jpa" version "${versions.kotlinVersion}"
        id "org.jetbrains.kotlin.plugin.noarg" version "${versions.kotlinVersion}"
        id "org.jetbrains.kotlin.plugin.spring" version "${versions.kotlinVersion}"
        id "org.owasp.dependencycheck" version "${versions.dependencyCheckGradleVersion}"
        id "org.springframework.boot" version "${versions.springBootVersion}"
    }
}

plugins {
    id "com.gradle.enterprise"
}

rootProject.name = "nova-acc-offer-consumer"
include(
        "nova-acc-offer-consumer-service",
        "nova-acc-offer-consumer-test",
)

