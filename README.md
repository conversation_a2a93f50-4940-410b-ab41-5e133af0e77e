# nova-acc-offer-consumer


Nova Acc Offer Consumer Lambda is responsible for sending offers to Adobe Creative Cloud.
The lambda is being triggered by a Kinesis Stream that receives offer published by POST

## Short retry environment variables
change env vars
deploy the new version from lambda console
make alias to point to new version
make the weight on previous version to 0
(worst case scenario you will end up removing and regenerating alias=live)

## ACC Configuration

ACC configuration are stored on AWS Secrets Manager, getting populated with a default value by YAML template file.</br>
When a release is going to be done, DX-Ops is asked to update these AWS Secrets Manager values by the real configuration.<br/>
The source of truth for these values can be found at [1Password](https://loyaltyone.1password.com/).

## Lambda Diagram 

![Lambda Diagram](nova-acc-offer-consumer-lambda-diagram.png)

## Scorecard

[Scorecard - Level 2 - API](scorecard.md) 

.