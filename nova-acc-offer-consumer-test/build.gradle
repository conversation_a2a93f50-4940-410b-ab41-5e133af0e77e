apply plugin: 'kotlin-spring'
apply plugin: 'eclipse'

dependencies {
    implementation(
            'ch.qos.logback.contrib:logback-jackson',
            'ch.qos.logback.contrib:logback-json-classic',
            'ch.qos.logback:logback-classic',
            'com.amazonaws:aws-java-sdk-core',
            'com.amazonaws:aws-java-sdk-lambda',
            'com.amazonaws:DynamoDBLocal',
            'com.fasterxml.jackson.core:jackson-annotations',
            'com.fasterxml.jackson.core:jackson-databind',
            'com.fasterxml.jackson.module:jackson-module-kotlin',
            'com.loyalty.nova:nova-common',
            'com.loyalty.nova:nova-common-events',
            'com.loyalty.nova:nova-common-test',
            'io.cucumber:cucumber-java8',
            'io.cucumber:cucumber-junit',
            'io.cucumber:cucumber-spring',
            'io.kotlintest:kotlintest-extensions-spring',
            'org.apache.commons:commons-lang3',
            'org.apache.httpcomponents:httpclient',
            'org.apache.httpcomponents:httpmime',
            'org.apache.jmeter:ApacheJMeter_core',
            'org.hamcrest:java-hamcrest',
            'org.jetbrains.kotlin:kotlin-compiler-embeddable',
            'org.jetbrains.kotlin:kotlin-reflect',
            'org.jetbrains.kotlin:kotlin-script-runtime',
            'org.jetbrains.kotlin:kotlin-script-util',
            'org.jetbrains.kotlin:kotlin-scripting-compiler-embeddable',
            'org.jetbrains.kotlin:kotlin-stdlib-jdk8',
            'org.jetbrains.kotlin:kotlin-test',
            'org.mockito:mockito-core',
            'org.springframework.boot:spring-boot-starter-data-rest',
            'org.springframework.boot:spring-boot-starter-test',
            'org.springframework:spring-context',
            'com.github.tomakehurst:wiremock-jre8',
            'com.amazonaws:aws-lambda-java-core',
            'com.amazonaws:aws-lambda-java-events',
            'com.amazonaws:aws-java-sdk-kinesis'
    )
}

configurations {
    integrationImplementation.extendsFrom implementation
    integrationRuntimeOnly.extendsFrom runtime
    cucumberRuntime.extendsFrom runtime
}

task integrationTest() {
    dependsOn assemble, compileTestJava
    doLast {
        javaexec {
            main = "cucumber.api.cli.Main"
            classpath = configurations.cucumberRuntime + sourceSets.main.output + sourceSets.main.runtimeClasspath
            args = ['--plugin', 'pretty',
                    '--plugin', 'html:build/reports/cucumber/cucumber-html-report',
                    '--plugin', 'json:build/reports/cucumber/' + cucumberTag.substring(1) + '-cucumber-json-report.json',
                    '--strict',
                    '--threads', '1',
                    '--glue', 'com.loyalty.nova.acc.offer.consumer.test',
                    '--glue', 'com.loyalty.nova.common.test.integration',
                    '--glue', 'com.loyalty.nova.common.test.performance',
                    '--tags', cucumberTag,
                    'src/main/resources/features']
            jvmArgs = ['-ea', '-Djava.library.path=build/libs', '-Djmeter.home=src/main/resources/jmeter-home', '-Duser.timezone=America/Toronto', '-Xms2024m', '-Dspring.profiles.active=' + profiles]
        }
    }
}
