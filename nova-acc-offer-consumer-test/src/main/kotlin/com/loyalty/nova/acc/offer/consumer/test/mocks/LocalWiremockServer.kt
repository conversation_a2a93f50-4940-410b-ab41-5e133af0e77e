package com.loyalty.nova.acc.offer.consumer.test.mocks

import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.client.WireMock
import com.github.tomakehurst.wiremock.core.WireMockConfiguration

fun main() {
    val wireMockServer = WireMockServer(
        WireMockConfiguration
            .options()
            .port(9090)
    )

    wireMockServer.start()

    WireMock.configureFor("localhost", 9090)
    WireMock.removeAllMappings()

    AccApiMock().mockFor(AccApiMock.StubType.token_ok)
    AccApiMock().mockFor(AccApiMock.StubType.token_error)
    AccApiMock().mockFor(AccApiMock.StubType.offer_insert_ok)
    AccApiMock().mockFor(AccApiMock.StubType.offer_update_ok)
    AccApiMock().mockFor(AccApiMock.StubType.offer_insertupdate_error)

    /**
     * Just setup Sanity Check *
    BasicApiMock().mockFor(BasicApiMock.StubType.badRequest)
    BasicApiMock().mockFor(BasicApiMock.StubType.internalServerError)
    BasicApiMock().mockFor(BasicApiMock.StubType.notFound)
    BasicApiMock().mockFor(BasicApiMock.StubType.ok)
    BasicApiMock().mockFor(BasicApiMock.StubType.post_ok)
    */
}
