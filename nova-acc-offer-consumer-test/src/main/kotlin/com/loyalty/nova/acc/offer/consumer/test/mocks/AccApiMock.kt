package com.loyalty.nova.acc.offer.consumer.test.mocks

import com.github.tomakehurst.wiremock.client.WireMock
import com.github.tomakehurst.wiremock.http.Fault

class AccApiMock {
    enum class StubType {
        token_ok, token_error, offer_insert_ok, offer_update_ok, offer_insertupdate_error
    }

    fun mockFor(type: StubType) {
        when (type) {
            StubType.token_ok -> {
                WireMock.stubFor(
                    WireMock.post(WireMock.urlMatching("^/nl/jsp/soaprouter.jsp(.*)"))
                        .withRequestBody(WireMock.matchingXPath("//strLogin[1]"))
                        .willReturn(
                            WireMock.aResponse()
                                .withStatus(200)
                                .withHeader("Content-Type", "text/xml")
                                .withHeader("SOAPAction", "xtk:session#Logon")
                                .withBody(
                                    AccApiMock::class.java.getResource(
                                        "/mocks_responses/response-create-token.xml"
                                    ).readText()
                                )
                        )
                )
            }
            StubType.token_error -> {
                WireMock.stubFor(
                    WireMock.post(WireMock.urlMatching("^/nl/jsp/soaprouter.jsp(.*)"))
                        .withRequestBody(WireMock.matchingXPath("//strLogin[1]"))
                        .willReturn(
                            WireMock.aResponse()
                                .withFault(Fault.MALFORMED_RESPONSE_CHUNK)
                        )
                )
            }
            StubType.offer_insert_ok -> {
                WireMock.stubFor(
                    WireMock.post(WireMock.urlMatching("^/nl/jsp/soaprouter.jsp(.*)"))
                        .withRequestBody(WireMock.matchingXPath("//offers[1]"))
                        .willReturn(
                            WireMock.aResponse()
                                .withStatus(200)
                                .withHeader("Content-Type", "text/xml")
                                .withHeader("SOAPAction", "nms:offer#insertUpdateOffers")
                                .withBody(
                                    AccApiMock::class.java.getResource(
                                        "/mocks_responses/response-create-offer.xml"
                                    ).readText()
                                )
                        )
                )
            }

            StubType.offer_update_ok -> {
                WireMock.stubFor(
                    WireMock.post(WireMock.urlMatching("^/nl/jsp/soaprouter.jsp(.*)"))
                        .withRequestBody(WireMock.matchingXPath("//offer[@updatedAt!=''][1]"))
                        .willReturn(
                            WireMock.aResponse()
                                .withStatus(200)
                                .withHeader("Content-Type", "text/xml")
                                .withHeader("SOAPAction", "nms:offer#insertUpdateOffers")
                                .withBody(
                                    AccApiMock::class.java.getResource(
                                        "/mocks_responses/response-update-offer.xml"
                                    ).readText()
                                )
                        )
                )
            }

            StubType.offer_insertupdate_error -> {
                WireMock.stubFor(
                    WireMock.post(WireMock.urlMatching("^/nl/jsp/soaprouter.jsp(.*)"))
                        .withRequestBody(WireMock.matchingXPath("//offer[@campaignCode='faulty_offer'][1]"))
                        .willReturn(
                            WireMock.aResponse()
                                .withFault(Fault.MALFORMED_RESPONSE_CHUNK)
                                )
                )
            }
        }
    }
}
