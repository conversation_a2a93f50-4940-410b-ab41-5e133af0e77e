package com.loyalty.nova.acc.offer.consumer.test

import com.loyalty.nova.common.test.integration.CommonTestConfig
import com.loyalty.nova.common.test.integration.RestCallContext
import com.loyalty.nova.common.test.integration.StepDefs
import com.loyalty.nova.common.test.integration.event.MessageContext
import com.loyalty.nova.common.test.integration.http.HttpCallContext
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.web.client.RestTemplate

@SpringBootTest(
    classes = [
        HttpCallContext::class,
        MessageContext::class,
        RestTemplate::class,
        RestCallContext::class,
        CommonTestConfig::class
    ]
)
class ConfigLoaderStepDefs : StepDefs
