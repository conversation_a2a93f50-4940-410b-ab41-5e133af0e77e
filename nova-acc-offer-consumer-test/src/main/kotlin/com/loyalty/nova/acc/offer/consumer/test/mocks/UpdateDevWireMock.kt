package com.loyalty.nova.acc.offer.consumer.test.mocks

import com.github.tomakehurst.wiremock.client.WireMock


//TODO: find an approach to add a similar step for CI/CD
fun main() {

    WireMock.configureFor("dev-public-wir-ELB-1X5GOSTKTJ4BG-1749143556.us-east-1.elb.amazonaws.com", 8080)
    WireMock.removeAllMappings()
    AccApiMock().mockFor(AccApiMock.StubType.token_ok)
    AccApiMock().mockFor(AccApiMock.StubType.token_error)
    AccApiMock().mockFor(AccApiMock.StubType.offer_insert_ok)
    AccApiMock().mockFor(AccApiMock.StubType.offer_update_ok)
    AccApiMock().mockFor(AccApiMock.StubType.offer_insertupdate_error)
}
