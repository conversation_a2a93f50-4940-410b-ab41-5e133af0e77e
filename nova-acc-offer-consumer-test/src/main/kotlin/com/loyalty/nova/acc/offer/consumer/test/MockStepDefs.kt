package com.loyalty.nova.acc.offer.consumer.test

import com.github.tomakehurst.wiremock.client.WireMock
import com.loyalty.nova.acc.offer.consumer.test.mocks.AccApiMock
import com.loyalty.nova.common.test.integration.StepDefs
import com.loyalty.nova.common.test.integration.http.HttpCallContext
import org.springframework.beans.factory.annotation.Autowired

@Suppress("TooGenericExceptionThrown", "TooGenericExceptionCaught")
class MockStepDefs : StepDefs {

    @Autowired
    private lateinit var context: HttpCallContext

    init {
        When("^running WireMock on host (\\S+) with port (\\d+)$") { host: String, port: Int ->
            WireMock.configureFor(host, port)
        }

        When("^resetting all WireMock requests$") {
            WireMock.resetAllRequests()
        }

        When("^stubbing AccApiMock (\\S+)$") { stubType: String ->
            AccApiMock().mockFor(AccApiMock.StubType.valueOf(stubType))
        }

        When("^verifying wiremock endpoint (\\S+) was called (\\d+) times") { endpoint: String, times: Int ->
            WireMock.verify(WireMock.exactly(times), WireMock.postRequestedFor(WireMock.urlEqualTo(endpoint)))
        }

        When("^verifying wiremock endpoint (\\S+) was not called") { endpoint: String ->
            WireMock.verify(WireMock.exactly(0), WireMock.postRequestedFor(WireMock.urlEqualTo(endpoint)))
        }
    }
}
