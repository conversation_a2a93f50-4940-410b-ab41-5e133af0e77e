Feature: Integration Setup Test

  Scenario Outline: Verify Test System Setup  - <description>

    Given running WireMock on host <wireMockHost> with port <wireMockPort>
    Then resetting all WireMock requests
    Then stubbing AccMockApi <mockStub>

#    @local
    Examples:
      | description | wireMockHost | wireMockPort | mockStub |
      | sanityCheck | localhost    | 9090         | post_ok  |

   # @sole
    Examples:
      | description | wireMockHost                                                            | wireMockPort | mockStub |
      | sanityCheck | dev-public-wir-ELB-1X5GOSTKTJ4BG-1749143556.us-east-1.elb.amazonaws.com | 8080         | token_ok |
