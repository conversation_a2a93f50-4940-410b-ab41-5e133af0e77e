Feature: E2E Test

  Scenario Outline: Verify Happy Path  - <description>

    Given running WireMock on host <wireMockHost> with port <wireMockPort>
    Given removing all WireMock mappings
    Given resetting all WireMock requests
    Then stubbing AccApiMock token_ok
    Then stubbing AccApiMock offer_insert_ok
    Then stubbing AccApiMock offer_update_ok
    # Receiving a published offer event
    When sending event request from file <file> using the following transformations:
      | path         | value                |
      | $.type       | <messageRequestType> |
      | $.lambdaName | <consumerLambdaName> |

    Then sleep 3000 ms
    Then verifying wiremock endpoint /nl/jsp/soaprouter.jsp was called 1 times

    @local
    Examples:
      | description  | messageRequestType | consumerLambdaName | wireMockHost | wireMockPort | file                                             |
      | Create Offer | kafka              |                    | localhost    | 9090         | /requests/OfferPublishedEventforCreateOffer.json |
      | Update Offer | kafka              |                    | localhost    | 9090         | /requests/OfferPublishedEventforUpdateOffer.json |

    @sole
    Examples:
      | description  | messageRequestType | consumerLambdaName                  | wireMockHost                                                            | wireMockPort | file                                             |
      | Create Offer | kotlinLambda       | sole-nova-acc-offer-consumer-lambda | dev-public-wir-ELB-1X5GOSTKTJ4BG-1749143556.us-east-1.elb.amazonaws.com | 8080         | /requests/OfferPublishedEventforCreateOffer.json |
      | Update Offer | kotlinLambda       | sole-nova-acc-offer-consumer-lambda | dev-public-wir-ELB-1X5GOSTKTJ4BG-1749143556.us-east-1.elb.amazonaws.com | 8080         | /requests/OfferPublishedEventforUpdateOffer.json |

  Scenario Outline: Retry Event - Token and Offer Failure  - <description>

    Given running WireMock on host <wireMockHost> with port <wireMockPort>
    Given removing all WireMock mappings
    Given resetting all WireMock requests
    Then stubbing AccApiMock token_error
    Then stubbing AccApiMock offer_insertupdate_error
    # Receiving a published offer event
    When sending event request from file <file> using the following transformations:
      | path         | value                |
      | $.type       | <messageRequestType> |
      | $.lambdaName | <consumerLambdaName> |

    Then sleep 3000 ms
    Then verifying wiremock endpoint /nl/jsp/soaprouter.jsp was called 3 times

    @local
    Examples:
      | description  | messageRequestType | consumerLambdaName | wireMockHost | wireMockPort | file                                             |
      | Faulty Event | kafka              |                    | localhost    | 9090         | /requests/OfferPublishedEventforFaultyOffer.json |

    @sole
    Examples:
      | description  | messageRequestType | consumerLambdaName                  | wireMockHost                                                            | wireMockPort | file                                             |
      | Faulty Event | kotlinLambda       | sole-nova-acc-offer-consumer-lambda | dev-public-wir-ELB-1X5GOSTKTJ4BG-1749143556.us-east-1.elb.amazonaws.com | 8080         | /requests/OfferPublishedEventforFaultyOffer.json |


  Scenario Outline: Retry Event - Offer Failure  - <description>

    Given running WireMock on host <wireMockHost> with port <wireMockPort>
    Given removing all WireMock mappings
    Given resetting all WireMock requests
    Then stubbing AccApiMock token_ok
    Then stubbing AccApiMock offer_insertupdate_error
    # Receiving a published offer event
    When sending event request from file <file> using the following transformations:
      | path         | value                |
      | $.type       | <messageRequestType> |
      | $.lambdaName | <consumerLambdaName> |

    Then sleep 3000 ms
    Then verifying wiremock endpoint /nl/jsp/soaprouter.jsp was called 3 times

    @local
    Examples:
      | description  | messageRequestType | consumerLambdaName | wireMockHost | wireMockPort | file                                             |
      | Faulty Event | kafka              |                    | localhost    | 9090         | /requests/OfferPublishedEventforFaultyOffer.json |

    @sole
    Examples:
      | description  | messageRequestType | consumerLambdaName                  | wireMockHost                                                            | wireMockPort | file                                             |
      | Faulty Event | kotlinLambda       | sole-nova-acc-offer-consumer-lambda | dev-public-wir-ELB-1X5GOSTKTJ4BG-1749143556.us-east-1.elb.amazonaws.com | 8080         | /requests/OfferPublishedEventforFaultyOffer.json |

