request_token:
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xtk="urn:xtk:session"><SOAP-ENV:Header/><SOAP-ENV:Body><xtk:Logon><sessiontoken/><strLogin>DUMMY_USER</strLogin><strPassword>DUMMY_PASSWORD</strPassword><elemParameters/></xtk:Logon></SOAP-ENV:Body></SOAP-ENV:Envelope>

response_token:
<?xml version='1.0'?><SOAP-ENV:Envelope xmlns:xsd='http://www.w3.org/2001/XMLSchema' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance' xmlns:ns='urn:xtk:session' xmlns:SOAP-ENV='http://schemas.xmlsoap.org/soap/envelope/'><SOAP-ENV:Body><LogonResponse xmlns='urn:xtk:session' SOAP-ENV:encodingStyle='http://schemas.xmlsoap.org/soap/encoding/'><pstrSessionToken xsi:type='xsd:string'>___309b2e6c-bb97-414f-b4ca-e3565b2778a1</pstrSessionToken><pSessionInfo xsi:type='ns:Element' SOAP-ENV:encodingStyle='http://xml.apache.org/xml-soap/literalxml'><sessionInfo><serverInfo advisedClientBuildNumber="9234" allowSQL="false" buildNumber="9234" commitId="662948b" databaseId="u0A5CE5ED00244616" defaultNameSpace="L1" fohVersion="2" instanceName="loyalty_mkt_stage1" majNumber="6" minClientBuildNumber="8969" minNumber="7" minNumberTechnical="0" releaseName="20.3" securityTimeOut="86400" serverDate="2021-03-25 12:17:48.194Z" servicePack="0" sessionTimeOut="86400" useVault="false"/><userInfo datakitInDatabase="true" homeDir="" instanceLocale="en-US" locale="en-US" login="DUMMY_USER" loginCS="Dummy User (DUMMY_USER)" loginId="3375130" noConsoleCnx="true" orgUnitId="0" theme="" timezone="America/New_York"><login-group id="1590"/><login-right right="folderEdit"/><login-right right="folderInsert"/><installed-package name="interactionExec" namespace="nms"/><installed-package name="databaseUsage" namespace="acx"/><installed-package name="importExportsCELO" namespace="L1"/><installed-package name="sPKG17ACX" namespace="L1"/><installed-package name="L1_additional_functions" namespace="L1"/><installed-package name="response" namespace="nms"/><installed-package name="sPKG5" namespace="L1"/><installed-package name="core" namespace="acx"/><installed-package name="campaignOptimization" namespace="nms"/><installed-package name="systemStrings" namespace="nms"/><installed-package name="ruleset" namespace="nms"/><installed-package name="billing" namespace="nms"/><installed-package name="survey" namespace="nms"/><installed-package name="azuredw" namespace="nms"/><installed-package name="teradata" namespace="nms"/><installed-package name="federatedDataAccess" namespace="nms"/><installed-package name="heatmap" namespace="nms"/><installed-package name="importWkfDatahub" namespace="L1"/><installed-package name="mrm" namespace="nms"/><installed-package name="simulation" namespace="nms"/><installed-package name="interaction" namespace="nms"/><installed-package name="social" namespace="nms"/><installed-package name="paper" namespace="nms"/><installed-package name="mobile" namespace="nms"/><installed-package name="phone" namespace="nms"/><installed-package name="purl" namespace="nms"/><installed-package name="vertica" namespace="nms"/><installed-package name="core" namespace="nms"/><installed-package name="country" namespace="nms"/><installed-package name="coreInteraction" namespace="nms"/><installed-package name="japanLoc" namespace="nms"/><installed-package name="core" namespace="xtk"/><installed-package name="folder" namespace="nms"/><installed-package name="report" namespace="nms"/><installed-package name="fileManager" namespace="acx"/><installed-package name="campaign" namespace="nms"/><installed-package name="deliverability" namespace="nms"/><installed-package name="pPKG1prdstg" namespace="L1"/><installed-package name="hive" namespace="nms"/><installed-package name="snowflake" namespace="nms"/><installed-package name="mobileApp" namespace="nms"/><installed-package name="content" namespace="ncm"/><installed-package name="centralLocal" namespace="nms"/><installed-package name="connectors" namespace="crm"/><installed-package name="webAnalytics" namespace="nms"/><installed-package name="aemIntegration" namespace="nms"/><installed-package name="macIntegration" namespace="nms"/><installed-package name="aamIntegration" namespace="nms"/><installed-package name="messageCenter" namespace="nms"/><installed-package name="messageCenterControl" namespace="nms"/></userInfo></sessionInfo></pSessionInfo><pstrSecurityToken xsi:type='xsd:string'>@rBC8oJvd_bL84-5CnZNyCA9cQiUyrat2qVwPDBBX0PDgTEsrTG9E2zau3Jo42Kx8Elan9eb_jY02C1umDjX8YQ==</pstrSecurityToken></LogonResponse></SOAP-ENV:Body></SOAP-ENV:Envelope>

request_publish:
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:amo="http://airmiles.ca/events/offers" xmlns:nms="urn:nms:offer" xmlns:xtk="urn:xtk:session"><SOAP-ENV:Header/><SOAP-ENV:Body><nms:insertUpdateOffers><nms:sessiontoken>___309b2e6c-bb97-414f-b4ca-e3565b2778a1</nms:sessiontoken><nms:offer><offers><offer active="true" availability="[InStore]" awardType="FlatMiles" campaignCode="" createdAt="2021-03-24T18:50:49.667Z" createdBy="<EMAIL>" displayDate="2021-03-24T00:00:00" displayPriority="0" endDate="2021-12-10T23:59:59" id="976ae203-83af-4609-b991-10efac651f43" issuanceCode="" massOffer="true" eventBasedOffer="false" cardType="[]" programType="traditionalcore" offerCategory1="121fa2dd-1fab-4492-8c5d-fb34624f4dda" offerCategory2="" offerCategory3="" offerType="Buy" partnerId="879e50a8-803f-4988-adcc-391dfeba88d4" partnerName="Sobeys" partnerOfferId="" productBrand="" productName="" programPriority="0" promotionId="" publishedAt="2021-03-24T18:50:59.709Z" publishedBy="<EMAIL>" qualifier="Product" regions="[NL, SK, AB, MB, NB, ON, NS, PE]" startDate="2021-03-24T00:00:00" tags="[]" updatedAt="" updatedBy="" xtkschema="nms:offer"><awardShort enUs="20 Bonus Miles" frCa="20 milles en prime"/><image enUs="https://s3.amazonaws.com/uat-l1-amrpwl-post-images/processed-images/80b479a7-598c-4218-a022-601f65da098e" frCa="https://s3.amazonaws.com/uat-l1-amrpwl-post-images/processed-images/80b479a7-598c-4218-a022-601f65da098e"/><legalText enUs="* Offer valid from March 24, 2021 to December 10, 2021. Valid at participating Sobeys locations in Alberta, Manitoba, New Brunswick, Newfoundland and Labrador, Nova Scotia, Ontario, Prince Edward Island and Saskatchewan. Minimum eligible purchase must be spent in a single transaction. While supplies last. Product availability may vary by store. We reserve the right to limit quantities. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by LoyaltyOne, Co. Partner, Supplier and Retailer trademarks are owned by the respective Partner, Supplier or Retailer or authorized for their use in Canada." frCa="* Offre en vigueur du 24 mars 2021 au 10 décembre 2021. Valable dans toutes les succursales Sobeys participantes dans les régions suivantes : Alberta, Manitoba, Nouveau-Brunswick, Nouvelle-Écosse, Ontario, Saskatchewan, Terre-Neuve-et-Labrador et Île-du-Prince-Édouard. L’achat minimum admissible doit être effectué en une seule transaction. Jusqu’à épuisement des stocks. La disponibilité des produits peut varier selon le magasin. Nous nous réservons le droit d’appliquer une limite aux quantités. La carte AIR MILES doit être présentée au moment de l’achat. Peut se combiner avec d’autres offres et offres AIR MILES. md/mc Marque déposée/de commerce d'AM Royalties Limited Partnership, employée en vertu d'une licence par LoyaltyOne, Co. Les marques de commerce des partenaires, fournisseurs et détaillants sont la propriété respective de ces derniers et leur usage est autorisé au Canada."/><qualifierShort enUs="Buy 10 This is a test offer in-store*" frCa="Achetez 10 This is a test offer en magasin*"/><cashierInstruction enUs="" frCa=""/><description enUs="" frCa=""/><offerCategory1Label enUs="Travel" frCa="Voyage"/><offerCategory2Label enUs="" frCa=""/><offerCategory3Label enUs="" frCa=""/><promotionLabel enUs="" frCa=""/><mechanisms><mechanism mechanismType="NoAction"><mechanismLabel enUs="" frCa=""/><mechanismText enUs="" frCa=""/><mechanismTitle enUs="" frCa=""/><mechanismValue enUs="" frCa=""/></mechanism></mechanisms><tiers><tier awardValue="20.0" qualifierValue="10.0" tierID="976ae203-83af-4609-b991-10efac651f43_0"><awardLong enUs="20 Bonus Miles" frCa="20 milles en prime"/><qualifierLong enUs="Buy 10 This is a test offer in-store*" frCa="Achetez 10 This is a test offer en magasin*"/><content label="LocalizedString(enUS=This is a test offer, frCA=This is a test offer)" masterProduct="false" productSKU="" upc=""/></tier></tiers></offer></offers></nms:offer></nms:insertUpdateOffers></SOAP-ENV:Body></SOAP-ENV:Envelope>

response_publish_insert:
<?xml version='1.0'?><SOAP-ENV:Envelope xmlns:xsd='http://www.w3.org/2001/XMLSchema' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance' xmlns:ns='urn:nms:offer' xmlns:SOAP-ENV='http://schemas.xmlsoap.org/soap/envelope/'><SOAP-ENV:Body><insertUpdateOffersResponse xmlns='urn:nms:offer' SOAP-ENV:encodingStyle='http://schemas.xmlsoap.org/soap/encoding/'><result xsi:type='ns:Element' SOAP-ENV:encodingStyle='http://xml.apache.org/xml-soap/literalxml'><acResponse><offer insertOrUpdate="insert" offerId="976ae203-83af-4609-b991-10efac651f43" offerName="L1_Offer_8642680"/></acResponse></result></insertUpdateOffersResponse></SOAP-ENV:Body></SOAP-ENV:Envelope>

response_publish_update:
<?xml version='1.0'?><SOAP-ENV:Envelope xmlns:xsd='http://www.w3.org/2001/XMLSchema' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance' xmlns:ns='urn:nms:offer' xmlns:SOAP-ENV='http://schemas.xmlsoap.org/soap/envelope/'><SOAP-ENV:Body><insertUpdateOffersResponse xmlns='urn:nms:offer' SOAP-ENV:encodingStyle='http://schemas.xmlsoap.org/soap/encoding/'><result xsi:type='ns:Element' SOAP-ENV:encodingStyle='http://xml.apache.org/xml-soap/literalxml'><acResponse><offer insertOrUpdate="update" offerId="a2f6744a-e5f4-4946-a19a-2dace6e1a598" offerName="L1_Offer_8612111"/></acResponse></result></insertUpdateOffersResponse></SOAP-ENV:Body></SOAP-ENV:Envelope>



