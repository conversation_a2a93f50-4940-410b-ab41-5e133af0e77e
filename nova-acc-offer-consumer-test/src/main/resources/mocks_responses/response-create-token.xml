<?xml version='1.0'?>
<SOAP-ENV:Envelope xmlns:xsd='http://www.w3.org/2001/XMLSchema' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
                   xmlns:ns='urn:xtk:session' xmlns:SOAP-ENV='http://schemas.xmlsoap.org/soap/envelope/'>
    <SOAP-ENV:Body>
        <LogonResponse xmlns='urn:xtk:session' SOAP-ENV:encodingStyle='http://schemas.xmlsoap.org/soap/encoding/'>
            <pstrSessionToken xsi:type='xsd:string'>___c7b53d32-c12b-4188-8a25-336b48bca713</pstrSessionToken>
            <pSessionInfo xsi:type='ns:Element' SOAP-ENV:encodingStyle='http://xml.apache.org/xml-soap/literalxml'>
                <sessionInfo>
                    <serverInfo advisedClientBuildNumber="9234" allowSQL="false" buildNumber="9234" commitId="662948b"
                                databaseId="u0A5CE5ED00244616" defaultNameSpace="L1" fohVersion="2"
                                instanceName="loyalty_mkt_stage1" majNumber="6" minClientBuildNumber="8969"
                                minNumber="7" minNumberTechnical="0" releaseName="20.3" securityTimeOut="86400"
                                serverDate="2021-04-21 17:00:32.260Z" servicePack="0" sessionTimeOut="86400"
                                useVault="false"/>
                    <userInfo datakitInDatabase="true" homeDir="" instanceLocale="en-US" locale="en-US" login="DUMMY_USER"
                              loginCS="Dummy User (DUMMY_USER)" loginId="3375130" noConsoleCnx="true" orgUnitId="0" theme=""
                              timezone="America/New_York">
                        <login-group id="1590"/>
                        <login-right right="folderEdit"/>
                        <login-right right="folderInsert"/>
                        <installed-package name="interactionExec" namespace="nms"/>
                        <installed-package name="databaseUsage" namespace="acx"/>
                        <installed-package name="importExportsCELO" namespace="L1"/>
                        <installed-package name="sPKG17ACX" namespace="L1"/>
                        <installed-package name="L1_additional_functions" namespace="L1"/>
                        <installed-package name="response" namespace="nms"/>
                        <installed-package name="sPKG5" namespace="L1"/>
                        <installed-package name="core" namespace="acx"/>
                        <installed-package name="campaignOptimization" namespace="nms"/>
                        <installed-package name="systemStrings" namespace="nms"/>
                        <installed-package name="ruleset" namespace="nms"/>
                        <installed-package name="billing" namespace="nms"/>
                        <installed-package name="survey" namespace="nms"/>
                        <installed-package name="azuredw" namespace="nms"/>
                        <installed-package name="teradata" namespace="nms"/>
                        <installed-package name="federatedDataAccess" namespace="nms"/>
                        <installed-package name="heatmap" namespace="nms"/>
                        <installed-package name="importWkfDatahub" namespace="L1"/>
                        <installed-package name="mrm" namespace="nms"/>
                        <installed-package name="simulation" namespace="nms"/>
                        <installed-package name="interaction" namespace="nms"/>
                        <installed-package name="social" namespace="nms"/>
                        <installed-package name="paper" namespace="nms"/>
                        <installed-package name="mobile" namespace="nms"/>
                        <installed-package name="phone" namespace="nms"/>
                        <installed-package name="purl" namespace="nms"/>
                        <installed-package name="vertica" namespace="nms"/>
                        <installed-package name="core" namespace="nms"/>
                        <installed-package name="country" namespace="nms"/>
                        <installed-package name="coreInteraction" namespace="nms"/>
                        <installed-package name="japanLoc" namespace="nms"/>
                        <installed-package name="core" namespace="xtk"/>
                        <installed-package name="folder" namespace="nms"/>
                        <installed-package name="report" namespace="nms"/>
                        <installed-package name="fileManager" namespace="acx"/>
                        <installed-package name="campaign" namespace="nms"/>
                        <installed-package name="deliverability" namespace="nms"/>
                        <installed-package name="pPKG1prdstg" namespace="L1"/>
                        <installed-package name="hive" namespace="nms"/>
                        <installed-package name="snowflake" namespace="nms"/>
                        <installed-package name="mobileApp" namespace="nms"/>
                        <installed-package name="content" namespace="ncm"/>
                        <installed-package name="centralLocal" namespace="nms"/>
                        <installed-package name="connectors" namespace="crm"/>
                        <installed-package name="webAnalytics" namespace="nms"/>
                        <installed-package name="aemIntegration" namespace="nms"/>
                        <installed-package name="macIntegration" namespace="nms"/>
                        <installed-package name="aamIntegration" namespace="nms"/>
                        <installed-package name="messageCenter" namespace="nms"/>
                        <installed-package name="messageCenterControl" namespace="nms"/>
                    </userInfo>
                </sessionInfo>
            </pSessionInfo>
            <pstrSecurityToken xsi:type='xsd:string'>
                @HyCcfFRimqE8UXyFgO046FMZJq9fLr-uwVxDWeVZZZMLFXdr0i3HbMlGIyna4gSMhl9Gz9DDfaGOmU-qqZ5emg==
            </pstrSecurityToken>
        </LogonResponse>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>