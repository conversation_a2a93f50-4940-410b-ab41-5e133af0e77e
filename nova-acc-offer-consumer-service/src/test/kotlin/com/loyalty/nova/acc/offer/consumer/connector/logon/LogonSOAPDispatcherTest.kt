package com.loyalty.nova.acc.offer.consumer.connector.logon

import org.junit.jupiter.api.Test
import com.loyalty.nova.acc.offer.consumer.util.toXMLString
import io.mockk.mockkClass
import org.junit.jupiter.api.Tag
import org.springframework.boot.test.context.SpringBootTest
import java.io.InputStream
import java.net.URL
import javax.xml.soap.MessageFactory
import javax.xml.soap.SOAPConnectionFactory
import javax.xml.soap.SOAPMessage
import kotlin.test.assertEquals

@Tag("unit")
@SpringBootTest
class LogonSOAPDispatcherTest {

    private val soapConnectionFactory: SOAPConnectionFactory = mockkClass(SOAPConnectionFactory::class)

    private val url: URL = mockkClass(URL::class)

    private val dispatcher = LogonSOAPDispatcher(url, soapConnectionFactory)

    private lateinit var test1ExpectSOAPMessageXML: SOAPMessage

    @Test
    fun convertInputToSOAPMessageTest() {
        val input = LogonInput("test_username", "test_password")
        val soapMsg: SOAPMessage = dispatcher.convertInputToSOAPMessage(input)
        this.test1ExpectSOAPMessageXML = createLoginSOAPMessage()
        assertEquals(soapMsg.toXMLString(), this.test1ExpectSOAPMessageXML.toXMLString())
    }

    @Test
    fun convertSOAPMessageToOutputTest(){
        val logOut = LogonOutput("___c7b53d32-c12b-4188-8a25-336b48bca713", "@HyCcfFRimqE8UXyFgO046FMZJq9fLr-uwVxDWeVZZZMLFXdr0i3HbMlGIyna4gSMhl9Gz9DDfaGOmU-qqZ5emg==")
        this.test1ExpectSOAPMessageXML = createLogoutSOAPMessage()
        val logDetails: LogonOutput = dispatcher.convertSOAPMessageToOutput(this.test1ExpectSOAPMessageXML)
        assertEquals(logDetails.toString(), logOut.toString())
    }

    private fun createLoginSOAPMessage(): SOAPMessage {
        return this::class.java
            .getResourceAsStream("/LogonSOAPMessageFactoryTest/Test1ExpectSOAPMessage.xml")
            .bufferedReader()
            .readText()
            .toSOAPMessage()
    }

    private fun createLogoutSOAPMessage(): SOAPMessage {
        return this::class.java
            .getResourceAsStream("/LogonSOAPMessageFactoryTest/CreateLogonResponse.xml")
            .bufferedReader()
            .readText()
            .toSOAPMessage()
    }

    fun String.toSOAPMessage(): SOAPMessage
    {
        return this.byteInputStream().toSOAPMessage()
    }

    fun InputStream.toSOAPMessage(): SOAPMessage
    {
        return MessageFactory.newInstance().createMessage(null, this)
    }
}