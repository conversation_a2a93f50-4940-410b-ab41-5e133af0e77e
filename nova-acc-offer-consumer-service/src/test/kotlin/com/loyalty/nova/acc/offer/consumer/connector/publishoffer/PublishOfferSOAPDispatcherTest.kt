package com.loyalty.nova.acc.offer.consumer.connector.publishoffer


import org.junit.jupiter.api.Test
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.loyalty.nova.acc.offer.consumer.util.toXMLString
import io.mockk.mockkClass
import org.junit.jupiter.api.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import java.io.InputStream
import java.net.URL
import javax.xml.soap.MessageFactory
import javax.xml.soap.SOAPConnectionFactory
import javax.xml.soap.SOAPMessage
import kotlin.test.assertEquals

@Tag("unit")
@SpringBootTest
class PublishOfferSOAPDispatcherTest {

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    private val soapConnectionFactory: SOAPConnectionFactory = mockkClass(SOAPConnectionFactory::class)

    private val url: URL = mockkClass(URL::class)

    private val dispatcher = PublishOfferSOAPDispatcher(url, soapConnectionFactory)

    private fun createPublishOffer(): PublishOfferInput {
        return this::class.java
            .getResourceAsStream("/PublishOfferSOAPMessageFactoryTest/PublishOfferInput1.json")
            .bufferedReader()
            .readText()
            .let(this.objectMapper::readValue)
    }

    private fun createPublishOfferEventBased(): PublishOfferInput {
        return this::class.java
                .getResourceAsStream("/PublishOfferSOAPMessageFactoryTest/PublishOfferInputEventBased.json")
                .bufferedReader()
                .readText()
                .let(this.objectMapper::readValue)
    }

    private val expectSOAPMessage1: SOAPMessage = this::class.java
        .getResourceAsStream("/PublishOfferSOAPMessageFactoryTest/Test1ExpectSOAPMessageXML.xml")
        .bufferedReader()
        .readText()
        .toSOAPMessage()

    private val expectSOAPMessageEventBased: SOAPMessage = this::class.java
            .getResourceAsStream("/PublishOfferSOAPMessageFactoryTest/TestEventBasedExpectSOAPMessageXML.xml")
            .bufferedReader()
            .readText()
            .toSOAPMessage()

    private val offerResponseSOAP: SOAPMessage = this::class.java
        .getResourceAsStream("/PublishOfferSOAPMessageFactoryTest/CreateOfferResponse.xml")
        .bufferedReader()
        .readText()
        .toSOAPMessage()

    @Test
    fun convertInputToSOAPMessageTest() {
        var offerInput = createPublishOffer()
        val soapMsg: SOAPMessage = dispatcher.convertInputToSOAPMessage(offerInput)
        assertEquals(this.expectSOAPMessage1.toXMLString(),soapMsg.toXMLString())
    }

    @Test
    fun convertEventBasedInputToSOAPMessageTest() {
        var offerInput = createPublishOfferEventBased()
        val soapMsg: SOAPMessage = dispatcher.convertInputToSOAPMessage(offerInput)
        assertEquals(this.expectSOAPMessageEventBased.toXMLString(),soapMsg.toXMLString())
    }

    @Test
    fun convertSOAPMessageToOutputTest(){
        val testLogOut = PublishOfferOutput("insert", "L1_Offer_9524862")
        val logDetails: PublishOfferOutput = dispatcher.convertSOAPMessageToOutput(offerResponseSOAP)
        assertEquals(logDetails.toString(), testLogOut.toString())
    }

    fun String.toSOAPMessage(): SOAPMessage
    {
        return this.byteInputStream().toSOAPMessage()
    }

    fun InputStream.toSOAPMessage(): SOAPMessage
    {
        return MessageFactory.newInstance().createMessage(null, this)
    }
}