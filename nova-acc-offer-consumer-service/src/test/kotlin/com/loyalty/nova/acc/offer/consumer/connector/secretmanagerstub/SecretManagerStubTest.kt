package com.loyalty.nova.acc.offer.consumer.connector.secretmanagerstub

import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import com.loyalty.nova.acc.offer.consumer.connector.Token
import com.loyalty.nova.acc.offer.consumer.connector.secrets.Credentials
import com.loyalty.nova.acc.offer.consumer.connector.secrets.SecretsManagerStub
import org.junit.jupiter.api.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest

@Tag("unit")
@SpringBootTest
class SecretManagerStubTest {

    @Autowired
    private lateinit var stub: SecretsManagerStub

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Test
    fun getSecretForCredentialsTest() {
        val secretValueCredentials = """
            {
                "username": "DUMMY_USER",
                "password": "DUMMY_PASSWORD",
                "endpointURL": "http://localhost:9090/nl/jsp/soaprouter.jsp"
            }
            """.trimIndent()

        val testCred = objectMapper.readValue(secretValueCredentials, Credentials::class.java)
        val cred = stub.getSecretForCredentials()
        Assertions.assertEquals(testCred, cred)
    }

    @Test
    fun putSecretForTokenTest(){
        val token = Token("Test", "Test")
        stub.putSecretForToken(token)
        val testToken = stub.getSecretForToken()
        Assertions.assertEquals(token, testToken)
    }
}