package com.loyalty.nova.acc.offer.consumer.connector.tokenProvider

import org.junit.jupiter.api.Test
import com.loyalty.nova.acc.offer.consumer.connector.*
import com.loyalty.nova.acc.offer.consumer.connector.secrets.SecretsManagerStub
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import kotlin.test.assertEquals

@Tag("unit2")
@SpringBootTest
class SecretManagerTokenProviderTest {

    @Autowired
    private lateinit var smStub: SecretsManagerStub

    private var mockTokenProvider : MockTokenProvider =
        MockTokenProvider(
            Token(
                sessionToken = "ACC_SESSION_TOKEN",
                securityToken = "ACC_SECURITY_TOKEN"
            )
        )

    private var noopTokenProvider: NoopTokenProvider = NoopTokenProvider()

//    @Test
//    fun getTokenSuccessTest() {
//        var secretManagerProvider = SecretsManagerTokenProvider(smStub, mockTokenProvider)
//        val expectedToken = Token(
//            sessionToken = "ACC_SESSION_TOKEN",
//            securityToken = "ACC_SECURITY_TOKEN"
//        )
//        var actualToken: Token = secretManagerProvider.getToken(true)
//        assertEquals(expectedToken.toString(), actualToken.toString())
//    }
//
//    @Test
//    fun getTokenFailureTest() {
//        var secretManagerProvider  = SecretsManagerTokenProvider(smStub, mockTokenProvider)
//        val expectedToken = Token(
//            sessionToken = "DUMMY_SESSION_TOKEN",
//            securityToken = "DUMMY_SECURITY_TOKEN"
//        )
//        var actualToken: Token = secretManagerProvider .getToken(false)
//        assertEquals(expectedToken.toString(), actualToken.toString())
//    }
//
//    @Test
//    fun getTokenThrowException() {
//        var secretManagerProvider  = SecretsManagerTokenProvider(smStub, noopTokenProvider)
//        val exception = assertThrows<NotImplementedError> { secretManagerProvider .getToken(true) }
//        assertEquals("An operation is not implemented: Not implemented, forceUpdateToken=true", exception.message)
//    }
}