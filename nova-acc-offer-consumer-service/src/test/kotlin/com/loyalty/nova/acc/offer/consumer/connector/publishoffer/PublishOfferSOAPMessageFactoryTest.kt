package com.loyalty.nova.acc.offer.consumer.connector.publishoffer

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.loyalty.nova.acc.offer.consumer.util.toXMLString
import org.junit.jupiter.api.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import java.io.InputStream
import javax.xml.soap.MessageFactory
import javax.xml.soap.SOAPMessage

@Tag("unit")
@SpringBootTest
class PublishOfferSOAPMessageFactoryTest {

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    private lateinit var publishOfferInput1: PublishOfferInput

    private fun createPublishOffer(): PublishOfferInput {
        return this::class.java
            .getResourceAsStream("/PublishOfferSOAPMessageFactoryTest/PublishOfferInput1.json")
            .bufferedReader()
            .readText()
            .let(this.objectMapper::readValue)
    }

    private val expectSOAPMessage1: SOAPMessage = this::class.java
            .getResourceAsStream("/PublishOfferSOAPMessageFactoryTest/Test1ExpectSOAPMessageXML.xml")
            .bufferedReader()
            .readText()
            .toSOAPMessage()

    @Test
    fun createSOAPMessageTest() {
        this.publishOfferInput1 = createPublishOffer()
        val publishOfferSOAPMessageFactory = PublishOfferSOAPMessageFactory(
                input = this.publishOfferInput1
        )
        val soapMessage = publishOfferSOAPMessageFactory.createSOAPMessage()
        val expectSOAPMessageXML = this.expectSOAPMessage1
        Assertions.assertEquals(expectSOAPMessageXML.toXMLString(), soapMessage.toXMLString())

    }
}

fun String.toSOAPMessage(): SOAPMessage
{
    return this.byteInputStream().toSOAPMessage()
}

fun InputStream.toSOAPMessage(): SOAPMessage
{
    return MessageFactory.newInstance().createMessage(null, this)
}
