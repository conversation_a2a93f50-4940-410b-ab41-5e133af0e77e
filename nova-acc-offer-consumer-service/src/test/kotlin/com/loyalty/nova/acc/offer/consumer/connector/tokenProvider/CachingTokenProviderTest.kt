package com.loyalty.nova.acc.offer.consumer.connector.tokenProvider

import com.loyalty.nova.acc.offer.consumer.connector.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.assertThrows
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.cache.concurrent.ConcurrentMapCache
import kotlin.test.assertEquals

@Tag("unit")
@SpringBootTest
class CachingTokenProviderTest {

    private var mockTokenProvider : MockTokenProvider =
        MockTokenProvider(
            Token(
                sessionToken = "ACC_SESSION_TOKEN",
                securityToken = "ACC_SECURITY_TOKEN"
            )
        )

    private var noopTokenProvider: NoopTokenProvider = NoopTokenProvider()

    @Test
    fun getTokenSuccessTest() {
        val cacheTokenProvider = CachingTokenProvider(
            name = "token",
            cache = ConcurrentMapCache("test"),
            tokenProvider = mockTokenProvider
        )
        val expectedToken = Token(
            sessionToken = "ACC_SESSION_TOKEN",
            securityToken = "ACC_SECURITY_TOKEN"
        )
        var actualToken: Token = cacheTokenProvider.getToken(true)
        assertEquals(expectedToken.toString(), actualToken.toString())
    }

    @Test
    fun getTokenFailureTest() {
        val cacheTokenProvider = CachingTokenProvider(
            name = "token",
            cache = ConcurrentMapCache("test"),
            tokenProvider = mockTokenProvider
        )
        val expectedToken = Token(
            sessionToken = "ACC_SESSION_TOKEN",
            securityToken = "ACC_SECURITY_TOKEN"
        )
        var actualToken: Token = cacheTokenProvider.getToken(false)
        assertEquals(actualToken.toString(), expectedToken.toString())
    }

    @Test
    fun getTokenThrowException() {
        val cacheTokenProvider = CachingTokenProvider(
            name = "token",
            cache = ConcurrentMapCache("test"),
            tokenProvider = noopTokenProvider
        )
        val exception = assertThrows<NotImplementedError> { cacheTokenProvider.getToken(true) }
        assertEquals("An operation is not implemented: Not implemented, forceUpdateToken=true", exception.message)
    }
}