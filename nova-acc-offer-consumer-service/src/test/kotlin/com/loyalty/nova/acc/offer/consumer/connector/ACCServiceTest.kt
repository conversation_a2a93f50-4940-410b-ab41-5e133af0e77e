package com.loyalty.nova.acc.offer.consumer.connector

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.loyalty.nova.acc.offer.consumer.connector.publishoffer.PublishOfferInput
import com.loyalty.nova.acc.offer.consumer.connector.publishoffer.PublishOfferOutput
import com.loyalty.nova.acc.offer.consumer.connector.publishoffer.PublishOfferSOAPDispatcher
import com.loyalty.nova.common.events.definitions.data.v3.OfferPublishedEventData
import io.mockk.every
import io.mockk.mockkClass
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest

@Tag("unit")
@SpringBootTest
class ACCServiceTest {
    @Autowired
    private lateinit var tokenProvider: TokenProvider

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Test
    fun publishOffer_withValidToken_dispatchesEvent() {
        val publishOfferSOAPDispatcher = mockkClass(PublishOfferSOAPDispatcher::class)
        val accService = ACCService(tokenProvider, publishOfferSOAPDispatcher)
        val offerPublishedEventData = createOfferPublishedEventData()

        every { publishOfferSOAPDispatcher.dispatch(any()) } returns PublishOfferOutput("", "")

        accService.publishOffer(offerPublishedEventData)

        verify {
            publishOfferSOAPDispatcher.dispatch(
                PublishOfferInput(
                    token = tokenProvider.getToken(false),
                    offerPublishedEventData = offerPublishedEventData
                )
            )
        }
    }

    @Test
    fun publishOffer_withInvalidEndpoint_logsError() {
        val offerPublishedEventData = createOfferPublishedEventData()

        val publishOfferSOAPDispatcher = mockkClass(PublishOfferSOAPDispatcher::class)

        val accService = ACCService(tokenProvider, publishOfferSOAPDispatcher)

        every { publishOfferSOAPDispatcher.dispatch(any()) } returns PublishOfferOutput("", "")

        // TODO - verify event has failed
    }

    private fun createOfferPublishedEventData(): OfferPublishedEventData {
        return this::class.java
            .getResourceAsStream("/OfferPublishedEventData.json")
            .bufferedReader()
            .readText()
            .let(this.objectMapper::readValue)
    }
}
