package com.loyalty.nova.acc.offer.consumer.connector.logon

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import com.loyalty.nova.acc.offer.consumer.util.toXMLString
import org.junit.jupiter.api.Tag
import org.springframework.boot.test.context.SpringBootTest
import java.io.InputStream
import javax.xml.soap.MessageFactory
import javax.xml.soap.SOAPMessage

@Tag("unit")
@SpringBootTest
class LogonSOAPMessageFactoryTest{

    private lateinit var test1ExpectSOAPMessageXML: SOAPMessage

    @Test
    fun createSOAPMessageTest() {
            val logonSOAPMessageFactory = LogonSOAPMessageFactory(
                    input = LogonInput(
                            username = "test_username",
                            password = "test_password"
                    )
            )
        val soapMessage = logonSOAPMessageFactory.createSOAPMessage()
        test1ExpectSOAPMessageXML = createLoginSOAPMessage()
        Assertions.assertEquals(this.test1ExpectSOAPMessageXML.toXMLString(), soapMessage.toXMLString())
    }


    private fun createLoginSOAPMessage(): SOAPMessage {
        return this::class.java
            .getResourceAsStream("/LogonSOAPMessageFactoryTest/Test1ExpectSOAPMessage.xml")
            .bufferedReader()
            .readText()
            .toSOAPMessage()
    }


    fun String.toSOAPMessage(): SOAPMessage
    {
        return this.byteInputStream().toSOAPMessage()
    }

    fun InputStream.toSOAPMessage(): SOAPMessage
    {
        return MessageFactory.newInstance().createMessage(null, this)
    }
}