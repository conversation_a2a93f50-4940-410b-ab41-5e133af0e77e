environment=local
server.port=2991
spring.cloud.stream.bindings.offer-event-bus-input.destination=offer-events-local
spring.cloud.stream.schema-registry-client.endpoint=http://localhost:8081
spring.cloud.stream.kafka.bindings.offer-event-bus-input.consumer.configuration.key.serializer=org.apache.kafka.common.serialization.StringSerializer
aws.region=us-east-1
spring.cloud.stream.kafka.binder.replicationFactor=1
spring.cloud.function.definition=offerPublishedEventFunction;persistentEntities;resourceMappings
#logging.level.org.springframework=DEBUG
#Investigate why setting log level to debug does not print the debug logs printed by logger.jsonDebug