package com.loyalty.nova.acc.offer.consumer.connector

import com.loyalty.nova.acc.offer.consumer.util.toQName
import javax.xml.namespace.QName
import javax.xml.soap.SOAPElement

class SOAPElementBuilder(
        private val soapElement: SOAPElement
)
{
    fun elem(qName: QName, block: SOAPElementBuilder.() -> Unit = {})
    {
        val childSOAPElementBuilder = SOAPElementBuilder(this.soapElement.addChildElement(qName))
        childSOAPElementBuilder.also(block)
    }

    fun elem(localPart: String, prefix: String, block: SOAPElementBuilder.() -> Unit = {})
    {
        val qName = QName(this.soapElement.lookupNamespaceURI(prefix), localPart, prefix)
        this.elem(qName, block)
    }

    fun elem(name: String, block: SOAPElementBuilder.() -> Unit = {})
    {
        val qName = name.toQName(this.soapElement)
        this.elem(qName, block)
    }

    fun attr(qName: QName, value: Any?)
    {
        this.soapElement.addAttribute(qName, value.toString())
    }

    fun attr(localPart: String, prefix: String, value: Any?)
    {
        val qName = QName(this.soapElement.lookupNamespaceURI(prefix), localPart, prefix)
        this.attr(qName, value)
    }

    fun attr(name: String, value: Any?)
    {
        val qName = name.toQName(this.soapElement)
        this.attr(qName, value)
    }

    fun text(text: String)
    {
        this.soapElement.addTextNode(text)
    }
}
