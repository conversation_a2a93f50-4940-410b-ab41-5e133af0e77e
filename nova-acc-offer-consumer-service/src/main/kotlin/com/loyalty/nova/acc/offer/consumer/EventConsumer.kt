package com.loyalty.nova.acc.offer.consumer

import com.loyalty.nova.acc.offer.consumer.connector.ACCService
import com.loyalty.nova.acc.offer.consumer.connector.TokenProvider
import com.loyalty.nova.acc.offer.consumer.connector.publishoffer.PublishOfferSOAPDispatcher
import com.loyalty.nova.acc.offer.consumer.connector.secrets.Credentials
import com.loyalty.nova.common.events.definitions.Event
import com.loyalty.nova.common.events.definitions.data.v3.OfferPublishedEventData
import com.loyalty.nova.common.events.definitions.meta.v1.EventMeta
import javax.xml.soap.SOAPConnectionFactory
import org.springframework.beans.factory.annotation.Autowired

abstract class EventConsumer {
    @Autowired
    private lateinit var credentials: Credentials

    @Autowired
    private lateinit var tokenProvider: TokenProvider

    abstract fun consumeEvent(event: Event<OfferPublishedEventData, EventMeta>)

    fun publishOffer(offerPublishedEventData: OfferPublishedEventData) {

        //TODO: Do we need a global try and catch here
        val publishOfferSOAPDispatcher = PublishOfferSOAPDispatcher(
            endpointURL = credentials.endpointURL,
            soapConnectionFactory = SOAPConnectionFactory.newInstance()
        )

        val accService = ACCService(
            tokenProvider = tokenProvider,
            publishOfferSOAPDispatcher = publishOfferSOAPDispatcher
        )

        accService.publishOffer(offerPublishedEventData)
    }
}
