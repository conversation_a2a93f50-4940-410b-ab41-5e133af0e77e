package com.loyalty.nova.acc.offer.consumer.connector.publishoffer

import com.loyalty.nova.acc.offer.consumer.connector.SOAPElementBuilder
import com.loyalty.nova.acc.offer.consumer.connector.SOAPMessageFactoryBase
import com.loyalty.nova.acc.offer.consumer.util.EMPTY
import com.loyalty.nova.common.events.definitions.data.v3.models.ExternalSystem
import com.loyalty.nova.common.events.definitions.data.v3.models.ProgramType
import java.time.format.DateTimeFormatter
import javax.xml.soap.SOAPBody

class PublishOfferSOAPMessageFactory(
        input: PublishOfferInput
) : SOAPMessageFactoryBase<PublishOfferInput>(
        input = input,
        soapAction = "nms:offer#insertUpdateOffers",
        namespaces = mapOf(
                "nms" to "urn:nms:offer",
                "xtk" to "urn:xtk:session",
                "amo" to "http://airmiles.ca/events/offers"
        )
)
{
    @Suppress("LongMethod", "ComplexMethod")
    override fun buildSOAPMessageBody(soapBody: SOAPBody, input: PublishOfferInput)
    {
        val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")

        SOAPElementBuilder(soapBody).elem("nms:insertUpdateOffers") {
            elem("nms:sessiontoken") {
                text(input.token.sessionToken)
            }

            elem("nms:offer") {
                elem("offers") {
                    elem("offer") {
                        attr("xtkschema", "nms:offer")
                        attr("id", input.offerPublishedEventData.id)

                        attr("active", input.offerPublishedEventData.active)
                        attr("availability", input.offerPublishedEventData.availability)
                        attr("agilityOfferCode", input.offerPublishedEventData.integrations.find { x -> x.systemName == ExternalSystem.Agility }?.systemCode)
                        /*
                        TODO: alternative implementation; should change to this but requires fix in the ACC JS code
                        elem("availability") {
                            attr("inStore", input.offerPublishedEventData.availability.contains(Availability.InStore))
                            attr("online", input.offerPublishedEventData.availability.contains(Availability.Online))
                        }
                        */

                        attr("awardType", input.offerPublishedEventData.awardType)
                        attr("createdAt", input.offerPublishedEventData.createdAt)
                        attr("createdBy", input.offerPublishedEventData.createdBy)
                        attr("displayDate", input.offerPublishedEventData.displayDate.format(dateTimeFormatter))
                        attr("displayPriority", input.offerPublishedEventData.displayPriority)
                        attr("endDate", input.offerPublishedEventData.endDate.format(dateTimeFormatter))
                        attr("massOffer", input.offerPublishedEventData.massOffer)
                        attr("eventBasedOffer", input.offerPublishedEventData.eventBasedOffer)
                        attr("usageLimit", input.offerPublishedEventData.usageLimit)
                        attr("retailerGroupId", input.offerPublishedEventData.retailerGroupId)
                        if (input.offerPublishedEventData.eventBasedOffer) {
                            attr("eligibilityDuration", input.offerPublishedEventData.eligibilityDuration)
                            attr("eligibilityDurationUnit", input.offerPublishedEventData.eligibilityDurationUnit)
                            attr("firstQualificationDate", input.offerPublishedEventData.firstQualificationDate)
                            attr("lastQualificationDate", input.offerPublishedEventData.lastQualificationDate)
                        }
                        if(input.offerPublishedEventData.ctaLabel!==null && input.offerPublishedEventData.ctaUrl!==null) {
                            attr("ctaLabel", input.offerPublishedEventData.ctaLabel)
                            attr("ctaUrl", input.offerPublishedEventData.ctaUrl)
                        }
                        attr("programType", input.offerPublishedEventData.programType)
                        attr("offerType", input.offerPublishedEventData.offerType)
                        attr("partnerId", input.offerPublishedEventData.partnerId)
                        attr("partnerName", input.offerPublishedEventData.partnerName)
                        attr("programType", input.offerPublishedEventData.programType)
                        attr("programPriority", input.offerPublishedEventData.programPriority)
                        attr("publishedAt", input.offerPublishedEventData.publishedAt)
                        attr("publishedBy", input.offerPublishedEventData.publishedBy)
                        attr("qualifier", input.offerPublishedEventData.qualifier)
                        attr("regions", input.offerPublishedEventData.regions)
                        attr("startDate", input.offerPublishedEventData.startDate.format(dateTimeFormatter))
                        attr("tags", input.offerPublishedEventData.tags)
                        if(input.offerPublishedEventData.programType===ProgramType.cardlinked) {
                            attr("cardType", input.offerPublishedEventData.cardType)
                        }
                        /*
                        TODO: alternative implementation; should change to this but requires fix in the ACC JS code
                        elem("tags") {
                            input.offerPublishedEventData.tags.forEach { tag ->
                                elem("tag") {
                                    attr("name", tag)
                                }
                            }
                        }
                        */
                        attr("campaignCode", input.offerPublishedEventData.campaignCode ?: String.EMPTY)
                        attr("issuanceCode", input.offerPublishedEventData.issuanceCode ?: String.EMPTY)
                        attr("offerCategory1", input.offerPublishedEventData.offerCategory1 ?: String.EMPTY)
                        attr("offerCategory2", input.offerPublishedEventData.offerCategory2 ?: String.EMPTY)
                        attr("offerCategory3", input.offerPublishedEventData.offerCategory3 ?: String.EMPTY)
                        attr("partnerOfferId", input.offerPublishedEventData.partnerOfferId ?: String.EMPTY)
                        attr("productBrand", input.offerPublishedEventData.productBrand ?: String.EMPTY)
                        attr("productName", input.offerPublishedEventData.productName ?: String.EMPTY)
                        attr("promotionId", input.offerPublishedEventData.promotionId ?: String.EMPTY)
                        attr("updatedAt", input.offerPublishedEventData.updatedAt ?: String.EMPTY)
                        attr("updatedBy", input.offerPublishedEventData.updatedBy ?: String.EMPTY)

                        elem("awardShort") {
                            attr("enUs", input.offerPublishedEventData.awardShort.enUS)
                            attr("frCa", input.offerPublishedEventData.awardShort.frCA)
                        }
                        elem("image") {
                            attr("enUs", input.offerPublishedEventData.image.enUS.path)
                            attr("frCa", input.offerPublishedEventData.image.frCA.path)
                        }
                        elem("legalText") {
                            attr("enUs", input.offerPublishedEventData.legalText.enUS)
                            attr("frCa", input.offerPublishedEventData.legalText.frCA)
                        }
                        elem("qualifierShort") {
                            attr("enUs", input.offerPublishedEventData.qualifierShort.enUS)
                            attr("frCa", input.offerPublishedEventData.qualifierShort.frCA)
                        }
                        elem("cashierInstruction") {
                            attr("enUs", input.offerPublishedEventData.cashierInstruction?.enUS ?: String.EMPTY)
                            attr("frCa", input.offerPublishedEventData.cashierInstruction?.frCA ?: String.EMPTY)
                        }
                        elem("description") {
                            attr("enUs", input.offerPublishedEventData.description?.enUS ?: String.EMPTY)
                            attr("frCa", input.offerPublishedEventData.description?.frCA ?: String.EMPTY)
                        }
                        elem("offerCategory1Label") {
                            attr("enUs", input.offerPublishedEventData.offerCategory1Label?.enUS ?: String.EMPTY)
                            attr("frCa", input.offerPublishedEventData.offerCategory1Label?.frCA ?: String.EMPTY)
                        }
                        elem("offerCategory2Label") {
                            attr("enUs", input.offerPublishedEventData.offerCategory2Label?.enUS ?: String.EMPTY)
                            attr("frCa", input.offerPublishedEventData.offerCategory2Label?.frCA ?: String.EMPTY)
                        }
                        elem("offerCategory3Label") {
                            attr("enUs", input.offerPublishedEventData.offerCategory3Label?.enUS ?: String.EMPTY)
                            attr("frCa", input.offerPublishedEventData.offerCategory3Label?.frCA ?: String.EMPTY)
                        }
                        elem("promotionLabel") {
                            attr("enUs", input.offerPublishedEventData.promotionLabel?.enUS ?: String.EMPTY)
                            attr("frCa", input.offerPublishedEventData.promotionLabel?.frCA ?: String.EMPTY)
                        }

                        elem("mechanisms") {
                            input.offerPublishedEventData.mechanisms.forEach { mechanism ->
                                elem("mechanism") {
                                    attr("mechanismType", mechanism.mechanismType)
                                    elem("mechanismLabel") {
                                        attr("enUs", mechanism.mechanismLabel?.enUS ?: String.EMPTY)
                                        attr("frCa", mechanism.mechanismLabel?.frCA ?: String.EMPTY)
                                    }
                                    elem("mechanismText") {
                                        attr("enUs", mechanism.mechanismText?.enUS ?: String.EMPTY)
                                        attr("frCa", mechanism.mechanismText?.frCA ?: String.EMPTY)
                                    }
                                    elem("mechanismTitle") {
                                        attr("enUs", mechanism.mechanismTitle?.enUS ?: String.EMPTY)
                                        attr("frCa", mechanism.mechanismTitle?.frCA ?: String.EMPTY)
                                    }
                                    elem("mechanismValue") {
                                        attr("enUs", mechanism.mechanismValue?.enUS ?: String.EMPTY)
                                        attr("frCa", mechanism.mechanismValue?.frCA ?: String.EMPTY)
                                    }
                                }
                            }
                        }

                        elem("tiers") {
                            input.offerPublishedEventData.tiers.forEachIndexed { index, tier ->
                                elem("tier") {
                                    attr("tierID", "${input.offerPublishedEventData.id}_${index}")
                                    attr("awardValue", tier.awardValue ?: 0)
                                    attr("qualifierValue", tier.qualifierValue ?: 0)
                                    attr("qualifierFrequency", tier.qualifierFrequency ?: 1)

                                    elem("awardLong") {
                                        attr("enUs", tier.awardLong.enUS)
                                        attr("frCa", tier.awardLong.frCA)
                                    }
                                    elem("qualifierLong") {
                                        attr("enUs", tier.qualifierLong.enUS)
                                        attr("frCa", tier.qualifierLong.frCA)
                                    }
                                    elem("content") {
                                        tier.content.forEach { content ->
                                            attr("label", content.label)
                                            /*
                                            TODO: alternative implementation; should change to this but requires fix in the ACC JS code
                                            elem("label") {
                                                attr("enUs", content.label?.enUS ?: String.EMPTY)
                                                attr("frCA", content.label?.frCA ?: String.EMPTY)
                                            }
                                            */

                                            attr("masterProduct", content.masterProduct ?: false)
                                            attr("productSKU", content.productSKU ?: String.EMPTY)
                                            attr("upc", content.upc ?: String.EMPTY)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
