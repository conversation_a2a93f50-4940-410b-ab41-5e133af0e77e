package com.loyalty.nova.acc.offer

import com.amazonaws.services.lambda.runtime.events.KinesisEvent
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.loyalty.nova.acc.offer.consumer.EventConsumer
import com.loyalty.nova.common.events.definitions.Event
import com.loyalty.nova.common.events.definitions.data.v3.OfferPublishedEventData
import com.loyalty.nova.common.events.definitions.meta.v1.EventMeta
import com.sun.xml.ws.transport.http.client.HttpTransportPipe
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
import org.springframework.boot.runApplication
import org.springframework.context.annotation.Bean
import java.nio.ByteBuffer
import java.util.function.Function

@SpringBootApplication(exclude = [DataSourceAutoConfiguration::class])
class ConsumerApp {
    @Autowired
    lateinit var eventConsumer: EventConsumer

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Bean
    fun offerPublishedEventFunction(): Function<KinesisEvent, Unit> {
        
        return Function { kinesisEvent: KinesisEvent ->
            kinesisEvent.records
                .map(KinesisEvent.KinesisEventRecord::getKinesis)
                .map(KinesisEvent.Record::getData)
                .map(ByteBuffer::array)
                .map { byteArray -> objectMapper.readValue<Event<OfferPublishedEventData, EventMeta>>(byteArray) }
                .forEach(this.eventConsumer::consumeEvent)

        }
    }

    companion object Main {
        @JvmStatic
        fun main(vararg args: String) {
            runApplication<ConsumerApp> {
                System.setProperty("${HttpTransportPipe::class.java.name}.dump", "true")
            }
        }
    }
}
