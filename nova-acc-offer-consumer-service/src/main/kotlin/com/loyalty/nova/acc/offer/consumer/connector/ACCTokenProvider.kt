package com.loyalty.nova.acc.offer.consumer.connector

import com.loyalty.nova.acc.offer.consumer.connector.logon.LogonInput
import com.loyalty.nova.acc.offer.consumer.connector.logon.LogonSOAPDispatcher
import com.loyalty.nova.acc.offer.consumer.connector.secrets.Credentials
import com.loyalty.nova.acc.offer.consumer.util.LogEventEnum
import com.loyalty.nova.common.logging.jsonError
import com.loyalty.nova.common.logging.logger
import javax.xml.soap.SOAPConnectionFactory

class ACCTokenProvider(private var credentials: Credentials) : TokenProvider {

    override fun getToken(forceUpdateToken: Boolean): Token {
        try {
            val logonSOAPDispatcher = LogonSOAPDispatcher(
                endpointURL = credentials.endpointURL,
                soapConnectionFactory = SOAPConnectionFactory.newInstance()
            )

            val logonOutput = logonSOAPDispatcher.dispatch(
                input = LogonInput(
                    username = credentials.username,
                    password = credentials.password
                )
            )
            return Token(
                sessionToken = logonOutput.sessionToken,
                securityToken = logonOutput.securityToken
            )
        }
        // TODO: Determine more specific exception type
        catch (@Suppress("TooGenericExceptionCaught") exception: Exception) {
            this.logger.jsonError(
                desc = LogEventEnum.CreateToken.value,
                value = mapOf(
                    "error" to exception.message,
                    "level" to "service"
                ),
                throwable = exception
            )
            throw exception
        }
    }
}
