package com.loyalty.nova.acc.offer.consumer

import com.loyalty.nova.common.events.definitions.Event
import com.loyalty.nova.common.events.definitions.data.v3.OfferPublishedEventData
import com.loyalty.nova.common.events.definitions.meta.v1.EventMeta
import org.springframework.cloud.stream.annotation.EnableBinding
import org.springframework.cloud.stream.annotation.StreamListener
import org.springframework.context.annotation.Profile

@Profile("local")
@EnableBinding(value = [OfferEventBusInput::class])
class KafkaEventConsumer : EventConsumer() {

    @StreamListener(OfferEventBusInput.INPUT)
    override fun consumeEvent(event: Event<OfferPublishedEventData, EventMeta>) {
        publishOffer(event.data)
    }
}
