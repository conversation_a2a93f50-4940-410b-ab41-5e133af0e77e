package com.loyalty.nova.acc.offer.consumer.connector.secrets

import com.fasterxml.jackson.databind.ObjectMapper
import com.loyalty.nova.acc.offer.consumer.connector.SecretsManager
import com.loyalty.nova.acc.offer.consumer.connector.Token
import org.springframework.beans.factory.config.BeanDefinition
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Profile
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Component

@Component
@Profile("local", "test")
class SecretsManagerStub(private val objectMapper: ObjectMapper) : SecretsManager {

    private var offlineSecretToken = """
        {
            "sessionToken": "DUMMY_SESSION_TOKEN",
            "securityToken": "DUMMY_SECURITY_TOKEN"
        }
        """.trimIndent()

    @Bean
    @Scope(value = BeanDefinition.SCOPE_SINGLETON)
    override fun getSecretForCredentials(): Credentials {
        val secretValueCredentials = """
            {
                "username": "DUMMY_USER",
                "password": "DUMMY_PASSWORD",
                "endpointURL": "http://localhost:9090/nl/jsp/soaprouter.jsp"
            }
            """.trimIndent()
        return objectMapper.readValue(secretValueCredentials, Credentials::class.java)
    }

    override fun getSecretForToken(): Token {
        return objectMapper.readValue(offlineSecretToken, Token::class.java)
    }

    override fun putSecretForToken(token: Token) {
        offlineSecretToken = objectMapper.writeValueAsString(token)
    }
}
