package com.loyalty.nova.acc.offer.consumer.connector

import com.loyalty.nova.acc.offer.consumer.util.LogEventEnum
import com.loyalty.nova.common.logging.jsonError
import com.loyalty.nova.common.logging.logger
import org.springframework.cache.Cache

class CachingTokenProvider(
        val name: String,
        val cache: <PERSON>ache,
        val tokenProvider: TokenProvider
) : TokenProvider
{
    override fun getToken(forceUpdateToken: Boolean): Token
    {
        try
        {
            var token = this.cache.get(this.name)?.get() as? Token
            if (token == null || forceUpdateToken)
            {
                token = this.tokenProvider.getToken(forceUpdateToken)
                this.cache.put(this.name, token)
            }
            return token
        }
        // TODO: Determine more specific exception type
        catch (@Suppress("TooGenericExceptionCaught") exception: Exception)
        {
            this.logger.jsonError(
                    desc = LogEventEnum.CreateToken.value,
                    value = mapOf(
                            "error" to exception.message,
                            "level" to "cache"
                    ),
                    throwable = exception
            )
            throw exception
        }
    }
}
