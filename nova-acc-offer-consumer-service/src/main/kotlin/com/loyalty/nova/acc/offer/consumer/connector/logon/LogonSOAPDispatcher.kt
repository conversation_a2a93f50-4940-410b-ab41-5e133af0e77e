package com.loyalty.nova.acc.offer.consumer.connector.logon

import com.loyalty.nova.acc.offer.consumer.connector.SOAPDispatcherBase
import com.loyalty.nova.acc.offer.consumer.util.LogEventEnum
import com.loyalty.nova.common.logging.jsonError
import com.loyalty.nova.common.logging.logger
import java.net.URL
import javax.xml.soap.SOAPConnectionFactory
import javax.xml.soap.SOAPMessage

class LogonSOAPDispatcher(
        override val endpointURL: URL,
        override val soapConnectionFactory: SOAPConnectionFactory
) : SOAPDispatcherBase<LogonInput, LogonOutput>()
{
    override fun convertInputToSOAPMessage(input: LogonInput): SOAPMessage
    {
        try
        {
            return LogonSOAPMessageFactory(input).createSOAPMessage()
        }
        // TODO: Determine more specific exception type
        catch (@Suppress("TooGenericExceptionCaught") exception: Exception)
        {
            this.logger.jsonError(
                    desc = LogEventEnum.TransformOffer.value,
                    value = mapOf(
                            "input" to "LogOnInput",
                            "function" to "convertInputToSOAPMessage"
                    ),
                    throwable = exception
            )
            throw exception
        }
    }

    override fun convertSOAPMessageToOutput(soapMessage: SOAPMessage): LogonOutput
    {
        try
        {
            return LogonOutput(
                    sessionToken = soapMessage.soapBody.getElementsByTagName("pstrSessionToken").item(0).textContent,
                    securityToken = soapMessage.soapBody.getElementsByTagName("pstrSecurityToken").item(0).textContent
            )
        }
        // TODO: Determine more specific exception type
        catch (@Suppress("TooGenericExceptionCaught") exception: Exception)
        {
            this.logger.jsonError(
                    desc = LogEventEnum.TransformOffer.value,
                    value = mapOf(
                            "output" to "LogOnOutput",
                            "function" to "convertSOAPMessageToOutput"
                    ),
                    throwable = exception
            )
            throw exception
        }
    }
}
