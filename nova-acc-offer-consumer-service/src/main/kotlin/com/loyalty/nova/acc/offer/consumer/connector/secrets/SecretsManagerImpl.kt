package com.loyalty.nova.acc.offer.consumer.connector.secrets

import com.amazonaws.services.secretsmanager.AWSSecretsManagerClientBuilder
import com.amazonaws.services.secretsmanager.model.GetSecretValueRequest
import com.amazonaws.services.secretsmanager.model.UpdateSecretRequest
import com.fasterxml.jackson.databind.ObjectMapper
import com.loyalty.nova.acc.offer.consumer.connector.SecretsManager
import com.loyalty.nova.acc.offer.consumer.connector.Token
import org.springframework.beans.factory.config.BeanDefinition
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Profile
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Component

@Component
@Profile(value = ["sole", "dev", "uat", "prod"])
class SecretsManagerImpl(
    private val objectMapper: ObjectMapper,
    private val secretsManagerConfiguration: SecretsManagerConfiguration
) : SecretsManager {

    @Bean
    @Scope(value = BeanDefinition.SCOPE_SINGLETON)
    override fun getSecretForCredentials(): Credentials {
        val client = AWSSecretsManagerClientBuilder.standard()
            .withRegion(secretsManagerConfiguration.awsRegion)
            .build()

        val getSecretValueRequest = GetSecretValueRequest().withSecretId(secretsManagerConfiguration.awsSecretForCredentials)
        val secretValueCredentials = client.getSecretValue(getSecretValueRequest).secretString

        return objectMapper.readValue(secretValueCredentials, Credentials::class.java)
    }

    override fun getSecretForToken(): Token {
        val client = AWSSecretsManagerClientBuilder.standard()
            .withRegion(secretsManagerConfiguration.awsRegion)
            .build()

        val getSecretValueRequest = GetSecretValueRequest().withSecretId(secretsManagerConfiguration.awsSecretForToken)
        val secretValueToken = client.getSecretValue(getSecretValueRequest).secretString
        return objectMapper.readValue(secretValueToken, Token::class.java)
    }

    override fun putSecretForToken(token: Token) {
        val client = AWSSecretsManagerClientBuilder.standard()
            .withRegion(secretsManagerConfiguration.awsRegion)
            .build()

        val updateSecretRequest = UpdateSecretRequest()
        updateSecretRequest.secretId = secretsManagerConfiguration.awsSecretForToken
        updateSecretRequest.secretString = objectMapper.writeValueAsString(token)
        client.updateSecret(updateSecretRequest)
    }
}
