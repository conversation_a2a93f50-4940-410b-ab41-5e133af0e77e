package com.loyalty.nova.acc.offer.consumer.connector

import javax.xml.soap.MessageFactory
import javax.xml.soap.SOAPBody
import javax.xml.soap.SOAPMessage

abstract class SOAPMessageFactoryBase<TInput>(
        private val input: TInput,
        private val soapAction: String,
        private val namespaces: Map<String, String>
) : SOAPMessageFactory
{
    override fun createSOAPMessage(): SOAPMessage
    {
        return MessageFactory.newInstance().createMessage()
                .also { soapMessage ->
                    this.namespaces.forEach { namespace ->
                        soapMessage.soapPart.envelope.addNamespaceDeclaration(namespace.key, namespace.value)
                    }
                    soapMessage.mimeHeaders.addHeader("SOAPAction", this.soapAction)
                    this.buildSOAPMessageBody(soapMessage.soapBody, this.input)
                }
    }

    protected abstract fun buildSOAPMessageBody(soapBody: SOAPBody, input: TInput)
}
