package com.loyalty.nova.acc.offer.consumer.connector.logon

import com.loyalty.nova.acc.offer.consumer.connector.SOAPElementBuilder
import com.loyalty.nova.acc.offer.consumer.connector.SOAPMessageFactoryBase
import javax.xml.soap.SOAPBody

class LogonSOAPMessageFactory(
        input: LogonInput
) : SOAPMessageFactoryBase<LogonInput>(
        input = input,
        soapAction = "xtk:session#Logon",
        namespaces = mapOf(
                "xtk" to "urn:xtk:session"
        )
)
{
    override fun buildSOAPMessageBody(soapBody: SOAPBody, input: LogonInput)
    {
        SOAPElementBuilder(soapBody).elem("xtk:Logon") {
            elem("sessiontoken")
            elem("strLogin") {
                text(input.username)
            }
            elem("strPassword") {
                text(input.password)
            }
            elem("elemParameters")
        }
    }
}
