package com.loyalty.nova.acc.offer.consumer.connector.publishoffer

import com.loyalty.nova.acc.offer.consumer.connector.SOAPDispatcherBase
import com.loyalty.nova.acc.offer.consumer.util.LogEventEnum
import com.loyalty.nova.acc.offer.consumer.util.toXMLString
import com.loyalty.nova.common.logging.jsonError
import com.loyalty.nova.common.logging.logger
import java.net.URL
import javax.xml.soap.SOAPConnectionFactory
import javax.xml.soap.SOAPMessage

class PublishOfferSOAPDispatcher(
        override val endpointURL: URL,
        override val soapConnectionFactory: SOAPConnectionFactory
) : SOAPDispatcherBase<PublishOfferInput, PublishOfferOutput>()
{
    override fun convertInputToSOAPMessage(input: PublishOfferInput): SOAPMessage
    {
        try
        {
            return PublishOfferSOAPMessageFactory(input).createSOAPMessage()
        }
        // TODO: Determine more specific exception type
        catch (@Suppress("TooGenericExceptionCaught") exception: Exception)
        {
            this.logger.jsonError(
                    desc = LogEventEnum.TransformOffer.value,
                    value = mapOf(
                            "input" to input.offerPublishedEventData,
                            "function" to "convertInputToSOAPMessage"
                    ),
                    throwable = exception
            )
            throw exception
        }
    }

    override fun convertSOAPMessageToOutput(soapMessage: SOAPMessage): PublishOfferOutput
    {
        try
        {
            val node = soapMessage.soapBody.getElementsByTagName("offer").item(0)
            return PublishOfferOutput(
                    action = node.attributes.getNamedItem("insertOrUpdate").nodeValue,
                    offerName = node.attributes.getNamedItem("offerName").nodeValue
            )
        }
        // TODO: Determine more specific exception type
        catch (@Suppress("TooGenericExceptionCaught") exception: Exception)
        {
            this.logger.jsonError(
                desc = LogEventEnum.TransformOffer.value,
                    value = mapOf(
                            "output" to soapMessage.toXMLString(),
                            "function" to "convertSOAPMessageToOutput"
                    ),
                    throwable = exception
            )
            throw exception
        }
    }
}
