package com.loyalty.nova.acc.offer.consumer.connector

import com.loyalty.nova.acc.offer.consumer.util.LogEventEnum
import com.loyalty.nova.common.logging.jsonError
import com.loyalty.nova.common.logging.logger

class SecretsManagerTokenProvider(
    private var secretsManager: SecretsManager,
    private val tokenProvider: TokenProvider
) : TokenProvider {

    override fun getToken(forceUpdateToken: Boolean): Token {
        try {
            if (forceUpdateToken) {
                val token = this.tokenProvider.getToken(forceUpdateToken)
                this.secretsManager.putSecretForToken(token)
                return token
            }
            return secretsManager.getSecretForToken()
        }
        // TODO: Determine more specific exception type
        catch (@Suppress("TooGenericExceptionCaught") exception: Exception) {
            try {
                val token = this.tokenProvider.getToken(forceUpdateToken)
                this.secretsManager.putSecretForToken(token)
                return token
            }
            // TODO: Determine more specific exception type
            catch (@Suppress("TooGenericExceptionCaught") exception: Exception) {
                this.logger.jsonError(
                    desc = LogEventEnum.CreateToken.value,
                    value = mapOf(
                        "error" to exception.message,
                        "level" to "secretManager"
                    ),
                    throwable = exception
                )
                throw exception
            }
        }
    }
}
