package com.loyalty.nova.acc.offer.consumer

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.loyalty.nova.acc.offer.consumer.connector.ACCTokenProvider
import com.loyalty.nova.acc.offer.consumer.connector.CachingTokenProvider
import com.loyalty.nova.acc.offer.consumer.connector.SecretsManager
import com.loyalty.nova.acc.offer.consumer.connector.SecretsManagerTokenProvider
import com.loyalty.nova.acc.offer.consumer.connector.TokenProvider
import com.loyalty.nova.acc.offer.consumer.connector.secrets.Credentials
import com.loyalty.nova.acc.offer.consumer.connector.secrets.SecretsManagerConfiguration
import org.springframework.beans.factory.annotation.Value
import org.springframework.beans.factory.config.BeanDefinition
import org.springframework.cache.concurrent.ConcurrentMapCache
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Scope
import org.springframework.beans.factory.config.ConfigurableBeanFactory

@Configuration
class SpringConfig {
    @Value("\${AWS_SECRET_CREDENTIALS:}")
    private lateinit var awsSecretForCredentials: String

    @Value("\${AWS_SECRET_TOKEN:}")
    private lateinit var awsSecretForToken: String

    private val awsRegion: String = "us-east-1"

    private val tokenCacheId: String = "acc-token"

    @Bean
    @Scope(value = BeanDefinition.SCOPE_SINGLETON)
    fun getObjectMapper(): ObjectMapper {
        return jacksonObjectMapper()
            .findAndRegisterModules()
            .registerKotlinModule().registerModule(JavaTimeModule())
            .configure(SerializationFeature.WRITE_DATE_KEYS_AS_TIMESTAMPS, false)
            .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true)
    }

    @Bean
    @Scope(value = ConfigurableBeanFactory.SCOPE_SINGLETON)
    fun getSecretsManagerConfiguration(): SecretsManagerConfiguration {
        return SecretsManagerConfiguration(
            awsRegion = awsRegion,
            awsSecretForCredentials = awsSecretForCredentials,
            awsSecretForToken = awsSecretForToken
        )
    }

    @Bean
    @Scope(value = BeanDefinition.SCOPE_SINGLETON)
    fun getTokenProvider(objectMapper: ObjectMapper, secretsManager: SecretsManager, credentials: Credentials): TokenProvider {
        return CachingTokenProvider(
            name = "token",
            cache = ConcurrentMapCache(this.tokenCacheId),
            tokenProvider = SecretsManagerTokenProvider(
                secretsManager = secretsManager,
                tokenProvider = ACCTokenProvider(credentials)
            )
        )
    }
}
