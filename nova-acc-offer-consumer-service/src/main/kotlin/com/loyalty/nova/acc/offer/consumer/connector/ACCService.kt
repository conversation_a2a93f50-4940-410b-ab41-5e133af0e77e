package com.loyalty.nova.acc.offer.consumer.connector

import com.loyalty.nova.acc.offer.consumer.connector.publishoffer.PublishOfferInput
import com.loyalty.nova.acc.offer.consumer.connector.publishoffer.PublishOfferSOAPDispatcher
import com.loyalty.nova.acc.offer.consumer.util.LogEventEnum
import com.loyalty.nova.common.events.definitions.data.v3.OfferPublishedEventData
import com.loyalty.nova.common.logging.jsonError
import com.loyalty.nova.common.logging.jsonInfo
import com.loyalty.nova.common.logging.logger

class ACCService(
    private val tokenProvider: TokenProvider,
    private val publishOfferSOAPDispatcher: PublishOfferSOAPDispatcher
) {

    fun publishOffer(offerPublishedEventData: OfferPublishedEventData) {
        val maxRetry = 1
        var count = 0
        var forceUpdateToken = false

        while (count <= maxRetry) {
            try {
                val token = tokenProvider.getToken(forceUpdateToken)
                val publishOfferOutput = publishOfferSOAPDispatcher.dispatch(
                    input = PublishOfferInput(
                        token = token,
                        offerPublishedEventData = offerPublishedEventData
                    )
                )
                this.logger.jsonInfo(
                    desc = LogEventEnum.PublishOffer.value,
                    value = mapOf(
                        "action" to publishOfferOutput.action,
                        "offerName" to publishOfferOutput.offerName,
                        "offer" to offerPublishedEventData
                    )
                )
                break
            }
            // TODO: Determine more specific exception type
            catch (@Suppress("TooGenericExceptionCaught") exception: Exception) {
                forceUpdateToken = true
                count++

                if (count > maxRetry) {
                    this.logger.jsonError(
                        desc = LogEventEnum.PublishOffer.value,
                        value = mapOf(
                            "offer" to offerPublishedEventData
                        ),
                        throwable = exception
                    )
                    // TODO: investigate what to return in failure case
                } else {
                    this.logger.jsonInfo(
                        desc = LogEventEnum.RetryOffer.value,
                        value = mapOf(
                            "offer" to offerPublishedEventData,
                        )
                    )
                }
            }
        }
    }
}
