package com.loyalty.nova.acc.offer.consumer

import com.loyalty.nova.common.events.definitions.Event
import com.loyalty.nova.common.events.definitions.data.v3.OfferPublishedEventData
import com.loyalty.nova.common.events.definitions.meta.v1.EventMeta
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Profile("!local")
@Service
class KinesisEventConsumer : EventConsumer() {

    override fun consumeEvent(event: Event<OfferPublishedEventData, EventMeta>) {
        publishOffer(event.data)
    }
}
