package com.loyalty.nova.acc.offer.consumer.connector

import com.loyalty.nova.acc.offer.consumer.connector.publishoffer.PublishOfferInput
import com.loyalty.nova.acc.offer.consumer.util.LogEventEnum
import com.loyalty.nova.acc.offer.consumer.util.toXMLString
import com.loyalty.nova.common.logging.jsonDebug
import com.loyalty.nova.common.logging.jsonError
import com.loyalty.nova.common.logging.jsonInfo
import com.loyalty.nova.common.logging.logger
import java.net.URL
import javax.xml.soap.SOAPConnectionFactory
import javax.xml.soap.SOAPMessage

abstract class SOAPDispatcherBase<TInput, TOutput> : Dispatcher<TInput, TOutput>
{
    protected abstract val endpointURL: URL
    protected abstract val soapConnectionFactory: SOAPConnectionFactory

    final override fun dispatch(input: TInput): TOutput
    {
        try
        {
            val soapConnection = this.soapConnectionFactory.createConnection()
            val requestSOAPMessage = this.convertInputToSOAPMessage(input)
            // TODO Investigate why below debug logs are not being printed locally,
            //  workaround change them to info if need them locally
            this.logger.jsonDebug(
                    desc = LogEventEnum.DispatchSoapRequest.value,
                    value = mapOf(
                            "url" to this.endpointURL,
                            "body" to requestSOAPMessage.toXMLString(),
                            "type" to "request"
                    )
            )
            val responseSOAPMessage = soapConnection.call(requestSOAPMessage, this.endpointURL)
            val responseString = responseSOAPMessage.toXMLString()
            if (!responseString.contains("pstrSecurityToken")) {
                this.logger.jsonInfo(
                    desc = LogEventEnum.DispatchSoapRequest.value,
                    value = mapOf(
                        "body" to responseString,
                        "type" to "response"
                    )
                )
            }
            return this.convertSOAPMessageToOutput(responseSOAPMessage)
        }
        // TODO: Determine more specific exception type
        catch (@Suppress("TooGenericExceptionCaught") exception: Exception)
        {
            this.logger.jsonError(
                    desc = LogEventEnum.DispatchSoapRequest.value,
                    value = mapOf(
                            "input" to when (input) {
                                is PublishOfferInput -> input.offerPublishedEventData
                                else -> "logOnInput"
                            }
                    ),
                    throwable = exception
            )
            throw exception
        }
    }

    abstract fun convertInputToSOAPMessage(input: TInput): SOAPMessage

    abstract fun convertSOAPMessageToOutput(soapMessage: SOAPMessage): TOutput
}
