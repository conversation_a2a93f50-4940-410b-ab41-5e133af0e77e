buildscript {
    ext.kotlin_version = "1.5.30"
    ext {
        springBootVersion = "2.3.1.RELEASE"
        wrapperVersion = "1.0.25.RELEASE"
        shadowVersion = "6.0.0"
    }
    repositories {
        maven { url "https://maven.pkg.jetbrains.space/public/p/kotlinx-html/maven/org/jetbrains/kotlinx/kotlinx-html/" }
        mavenCentral()
        gradlePluginPortal()
        google()
        mavenLocal()
        jcenter()
        maven { url "https://repo.spring.io/snapshot" }
        maven { url "https://repo.spring.io/milestone" }
    }
    dependencies {
        classpath "com.github.jengelman.gradle.plugins:shadow:${shadowVersion}"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath("io.spring.gradle:dependency-management-plugin:1.0.8.RELEASE")
        classpath("org.springframework.boot.experimental:spring-boot-thin-gradle-plugin:${wrapperVersion}")
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
    }
}

apply plugin: "com.github.johnrengelman.shadow"
apply plugin: "idea"
apply plugin: "java"
apply plugin: "kotlin-kapt"
apply plugin: "kotlin-noarg"
apply plugin: "kotlin-spring"
apply plugin: "org.springframework.boot"
apply plugin: "org.springframework.boot.experimental.thin-launcher"
apply plugin: "jacoco"

ext {
    springCloudFunctionVersion = "3.1.7"
}

assemble.dependsOn = [shadowJar, thinJar]

jar {
    manifest {
        attributes "Main-Class": "com.loyalty.nova.acc.offer.ConsumerApp"
    }
    enabled = true
}

import com.github.jengelman.gradle.plugins.shadow.transformers.PropertiesFileTransformer

shadowJar {
    zip64=true
    classifier = "aws"
    dependencies {
        exclude(dependency("org.apache.kafka:kafka-clients:.*"))
        exclude(dependency("org.springframework.cloud:spring-cloud-function-web:${springCloudFunctionVersion}"))
        exclude(dependency("org.springframework.cloud:spring-cloud-starter-stream-kafka:.*"))
        exclude(dependency("org.springframework.cloud:spring-cloud-stream-binder-kafka-core:.*"))
        exclude(dependency("org.springframework.cloud:spring-cloud-stream-binder-kafka:.*"))
        exclude(dependency("org.springframework.cloud:spring-cloud-stream:.*"))
        exclude(dependency("org.springframework.integration:spring-integration-kafka:.*"))
        exclude(dependency("org.springframework.kafka:spring-kafka:.*"))
    }
    // Required for Spring
    mergeServiceFiles()
    append "META-INF/spring.handlers"
    append "META-INF/spring.schemas"
    append "META-INF/spring.tooling"
    transform(PropertiesFileTransformer) {
        paths = ["META-INF/spring.factories"]
        mergeStrategy = "append"
    }
}
jacocoTestReport {
    reports {
        xml.enabled true
    }
}
dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-function-dependencies:${springCloudFunctionVersion}"
    }
}

dependencies {
    implementation(
            "org.springframework.cloud:spring-cloud-function-adapter-aws",
            "org.springframework.cloud:spring-cloud-function-core",
            "org.springframework.cloud:spring-cloud-function-web",
            "com.amazonaws:aws-lambda-java-log4j2",
            "com.amazonaws:aws-java-sdk-core",
            "com.amazonaws:aws-java-sdk-secretsmanager",
            "com.amazonaws:aws-lambda-java-core",
            "com.amazonaws:aws-lambda-java-events",
            "com.fasterxml.jackson.core:jackson-databind",
            "com.fasterxml.jackson.module:jackson-module-kotlin",
            "com.loyalty.nova:nova-common",
            "com.loyalty.nova:nova-common-events",
            "com.sun.xml.ws:jaxws-ri:2.3.2",
            "org.jetbrains.kotlin:kotlin-noarg",
            "org.jetbrains.kotlin:kotlin-reflect",
            "org.jetbrains.kotlin:kotlin-stdlib-jdk8",
            "org.springframework.boot:spring-boot-starter-integration",
            "org.springframework.cloud:spring-cloud-function-adapter-aws",
            "org.springframework.cloud:spring-cloud-starter-function-web",
            "org.springframework.cloud:spring-cloud-starter-function-webflux",
            "org.springframework.cloud:spring-cloud-starter-stream-kafka",
            "org.springframework:spring-context",
            "com.sun.xml.ws:jaxws-ri:2.3.2",
            'ch.qos.logback.contrib:logback-jackson',
            'ch.qos.logback.contrib:logback-json-classic',
    )
    annotationProcessor(
            "org.springframework.boot:spring-boot-configuration-processor",
    )
    kapt(
            "org.hibernate:hibernate-jpamodelgen",
    )
    testImplementation(
            "org.hamcrest:java-hamcrest",
            "org.jetbrains.kotlin:kotlin-test",
            "org.springframework.boot:spring-boot-starter-aop",
            "org.springframework.boot:spring-boot-starter-test",
            'io.mockk:mockk',
            'ch.qos.logback.contrib:logback-json-classic',
            'ch.qos.logback.contrib:logback-jackson'
    )
}