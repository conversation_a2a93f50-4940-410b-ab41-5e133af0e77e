ext.versions = [
        'awsLambdaJavaCoreVersion'         : '1.2.1',
        'awsLambdaJavaEventsVersion'       : '3.6.0',
        'awsServerlessJavaContainerVersion': '1.5.2',
        'amazonKinesisAggregator'          : '1.0.3',
        'cacheApiVersion'                  : '1.1.0',
        'cucumberVersion'                  : '4.2.0',
        'detektVersion'                    : '1.14.2',
        'dockerPluginVersion'              : '0.22.2',
        'DynamoDBLocalVersion'             : '1.13.5',
        'ehcacheVersion'                   : '3.9.0',
        'flywaydbVersion'                  : '7.3.0',
        'gradleEnterpriseVersion'          : '3.5',
        'gradleReleaseVersion'             : '2.6.0',
        'gradleTestLoggerPluginVersion'    : '2.1.1',
        'greenMailVersion'                 : '1.6.1',
        'guavaVersion'                     : '32.0.0-jre',
        'h2Version'                        : '1.4.200',
        'hamcrestVersion'                  : '2.0.0.0',
        'hibernateValidatorVersion'        : '6.1.6.Final',
        'httpClientVersion'                : '4.5.13',
        'jacksonDatabind'                  : '2.16.0',
        'jacksonVersion'                   : '2.16.0',
        'javafakerVersion'                 : '1.0.2',
        'jjwtVersion'                      : '0.11.2',
        'jmeterPluginsCasutgVersion'       : '2.9',
        'jmeterVersion'                    : '5.0',
        'jpamodelgenVersion'               : '5.4.24.Final',
        'junitJupiterVersion'              : '5.7.0',
        'kotlintestVersion'                : '3.4.2',
        'kotlinVersion'                    : '1.5.30',
        'kotlinxJvmVersion'                : '0.7.3',
        'kotlinxVersion'                   : '1.4.2',
        'kotlinjdk'                        : '1.5.30',
        'kotlinPluginVersion'              : '1.5.30',
        'logbackClassicVersion'            : '1.2.13',
        'logbackContrib'                   : '0.1.5',
        'mockitoJunitJupiterVersion'       : '3.6.28',
        'mockitoVersion'                   : '3.6.28',
        'novaCommonEventsVersion'          : '0.0.228',
        'novaCommonTestVersion'            : '0.0.165',
        'novaCommonVersion'                : '0.0.57',
        'nettyVersion'                     : '4.1.118.Final',
        'protobufJavaVersion'              : '3.25.5',
        'reactorBomVersion'                : '2020.0.1',
        'snakeYamlVersion'                 : '2.0',
        'springBootVersion'                : '2.4.0',
        'springCloudStreamVersion'         : '3.1.0',
        'springCloudVersion'               : '2020.0.0',
        'springDependencyManagementVersion': '1.0.10.RELEASE',
        'springMockVersion'                : '2.0.8',
        'sqlite4javaVersion'               : '1.0.392',
        'swaggerVersion'                   : '3.0.0',
        'typesafeConfigVersion'            : '1.3.3',
        'validationApiVersion'             : '2.0.1.Final',
        'wiremockVersion'                  : '2.27.2',
        'mockk'                            : '1.10.4',
        'awsJavaSdkS3Version'              : '1.12.639',
        'awsLambdaJavaLog4j2'              : '1.5.1',
        'springContextVersion'             : '3.2.6',
        'springCloudKafkaVersion'          : '3.1.0',
        'apacheLog4j'                      : '2.17.1',
        'woodstoxVersion'                  : '5.4.0'
]

ext.libraries = [
        'ApacheJMeter_core'                        : "org.apache.jmeter:ApacheJMeter_core:${versions.jmeterVersion}",
        'ApacheJMeter_http'                        : "org.apache.jmeter:ApacheJMeter_http:${versions.jmeterVersion}",
        'amazon-kinesis-aggregator'                : "com.amazonaws:amazon-kinesis-aggregator:${versions.amazonKinesisAggregator}",
        'amazon-kinesis-deaggregator'              : "com.amazonaws:amazon-kinesis-deaggregator:${versions.amazonKinesisAggregator}",
        'aws-java-sdk-s3'                          : "com.amazonaws:aws-java-sdk-s3:${versions.awsJavaSdkS3Version}",
        'aws-lambda-java-core'                     : "com.amazonaws:aws-lambda-java-core:${versions.awsLambdaJavaCoreVersion}",
        'aws-lambda-java-events'                   : "com.amazonaws:aws-lambda-java-events:${versions.awsLambdaJavaEventsVersion}",
        'aws-serverless-java-container-springboot2': "com.amazonaws.serverless:aws-serverless-java-container-springboot2:${versions.awsServerlessJavaContainerVersion}",
        'aws-lambda-java-log4j2'                   : "com.amazonaws:aws-lambda-java-log4j2:${versions.awsLambdaJavaLog4j2}",
        'aws-java-sdk-secretsmanager'              : "com.amazonaws:aws-java-sdk-secretsmanager:${versions.awsJavaSdkS3Version}",
        'amazon-kinesis'                           : "com.amazonaws:aws-java-sdk-kinesis:${versions.awsJavaSdkS3Version}",
        'aws-java-sdk-lambda'                      : "com.amazonaws:aws-java-sdk-lambda:${versions.awsJavaSdkS3Version}",
        'cache-api'                                : "javax.cache:cache-api:${versions.cacheApiVersion}",
        'cucumber-java8'                           : "io.cucumber:cucumber-java8:${versions.cucumberVersion}",
        'cucumber-junit'                           : "io.cucumber:cucumber-junit:${versions.cucumberVersion}",
        'cucumber-spring'                          : "io.cucumber:cucumber-spring:${versions.cucumberVersion}",
        'detekt'                                   : "io.gitlab.arturbosch.detekt:detekt-cli:${versions.detektVersion}",
        'detekt-formatting'                        : "io.gitlab.arturbosch.detekt:detekt-formatting:${versions.detektVersion}",
        'detekt-gradle-plugin'                     : "gradle.plugin.io.gitlab.arturbosch.detekt:detekt-gradle-plugin:${versions.detektVersion}",
        'DynamoDBLocal'                            : "com.amazonaws:DynamoDBLocal:${versions.DynamoDBLocalVersion}",
        'ehcache'                                  : "org.ehcache:ehcache:${versions.ehcacheVersion}",
        'flywaydb'                                 : "org.flywaydb:flyway-core:${versions.flywaydbVersion}",
        'gradle-docker'                            : "gradle.plugin.com.palantir.gradle.docker:gradle-docker:${versions.dockerPluginVersion}",
        'gradle-release'                           : "net.researchgate:gradle-release:${versions.gradleReleaseVersion}",
        'gradle-test-logger-plugin'                : "com.adarshr:gradle-test-logger-plugin:${versions.gradleTestLoggerPluginVersion}",
        'greenmail'                                : "com.icegreen:greenmail:${versions.greenMailVersion}",
        'guava'                                    : "com.google.guava:guava:${versions.guavaVersion}",
        'h2'                                       : "com.h2database:h2:${versions.h2Version}",
        'hibernate-jpamodelgen'                    : "org.hibernate:hibernate-jpamodelgen:${versions.jpamodelgenVersion}",
        'hibernateValidator'                       : "org.hibernate.validator:hibernate-validator:${versions.hibernateValidatorVersion}",
        'httpclient'                               : "org.apache.httpcomponents:httpclient:${versions.httpClientVersion}",
        'jackson-annotations'                      : "com.fasterxml.jackson.core:jackson-annotations:${versions.jacksonVersion}",
        'jackson-core'                             : "com.fasterxml.jackson.core:jackson-core:${versions.jacksonVersion}",
        'jackson-databind'                         : "com.fasterxml.jackson.core:jackson-databind:${versions.jacksonVersion}",
        'jackson-dataformat-cbor'                  : "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:${versions.jacksonVersion}",
        'jackson-module-kotlin'                    : "com.fasterxml.jackson.module:jackson-module-kotlin:${versions.jacksonVersion}",
        'java-hamcrest'                            : "org.hamcrest:java-hamcrest:${versions.hamcrestVersion}",
        'javafaker'                                : "com.github.javafaker:javafaker:${versions.javafakerVersion}",
        'jjwt-api'                                 : "io.jsonwebtoken:jjwt-api:${versions.jjwtVersion}",
        'jjwt-impl'                                : "io.jsonwebtoken:jjwt-impl:${versions.jjwtVersion}",
        'jjwt-jackson'                             : "io.jsonwebtoken:jjwt-jackson:${versions.jjwtVersion}",
        'jmeter-plugins-casutg'                    : "kg.apc:jmeter-plugins-casutg:${versions.jmeterPluginsCasutgVersion}",
        'junit-jupiter'                            : "org.junit.jupiter:junit-jupiter:${versions.junitJupiterVersion}",
        'kotlin-allopen'                           : "org.jetbrains.kotlin:kotlin-allopen:${versions.kotlinVersion}",
        'kotlin-gradle-plugin'                     : "org.jetbrains.kotlin:kotlin-gradle-plugin:${versions.kotlinVersion}",
        'kotlin-noarg'                             : "org.jetbrains.kotlin:kotlin-noarg:${versions.kotlinVersion}",
        'kotlin-reflect'                           : "org.jetbrains.kotlin:kotlin-reflect:${versions.kotlinVersion}",
        'kotlin-scripting-jvm'                     : "org.jetbrains.kotlin:kotlin-scripting-jvm:${versions.kotlinVersion}",
        'kotlin-stdlib'                            : "org.jetbrains.kotlin:kotlin-stdlib:${versions.kotlinVersion}",
        'kotlin-stdlib-jdk8'                       : "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${versions.kotlinVersion}",
        'kotlin-test'                              : "org.jetbrains.kotlin:kotlin-test:${versions.kotlinVersion}",
        'kotlintest-extensions-spring'             : "io.kotlintest:kotlintest-extensions-spring:${versions.kotlintestVersion}",
        'kotlinx-coroutines-core'                  : "org.jetbrains.kotlinx:kotlinx-coroutines-core:${versions.kotlinxVersion}",
        'kotlinx-html-jvm'                         : "org.jetbrains.kotlinx:kotlinx-html-jvm:${versions.kotlinxJvmVersion}",
        'libsqlite4java-linux-amd64'               : "com.almworks.sqlite4java:libsqlite4java-linux-amd64:${versions.sqlite4javaVersion}",
        'libsqlite4java-linux-i386'                : "com.almworks.sqlite4java:libsqlite4java-linux-i386:${versions.sqlite4javaVersion}",
        'libsqlite4java-osx'                       : "com.almworks.sqlite4java:libsqlite4java-osx:${versions.sqlite4javaVersion}",
        'logback-classic'                          : "ch.qos.logback:logback-classic:${versions.logbackClassicVersion}",
        'logback-core'                             : "ch.qos.logback:logback-core:${versions.logbackClassicVersion}",
        'logback-jackson'                          : "ch.qos.logback.contrib:logback-jackson:${versions.logbackContrib}",
        'logback-json-classic'                     : "ch.qos.logback.contrib:logback-json-classic:${versions.logbackContrib}",
        'log4j-core'                               : "org.apache.logging.log4j:log4j-core:${versions.apacheLog4j}",
        'log4j-api'                                : "org.apache.logging.log4j:log4j-api:${versions.apacheLog4j}",
        'mockito-core'                             : "org.mockito:mockito-core:${versions.mockitoVersion}",
        'mockito-junit-jupiter'                    : "org.mockito:mockito-junit-jupiter:${versions.mockitoJunitJupiterVersion}",
        'nova-common'                              : "com.loyalty.nova:nova-common:${versions.novaCommonVersion}",
        'nova-common-events'                       : "com.loyalty.nova:nova-common-events:${versions.novaCommonEventsVersion}",
        'nova-common-test'                         : "com.loyalty.nova:nova-common-test:${versions.novaCommonTestVersion}",
        'netty-handler'                            : "io.netty:netty-handler:${versions.nettyVersion}",
        'netty-codec'                              : "io.netty:netty-codec:${versions.nettyVersion}",
        'netty-codec-http'                         : "io.netty:netty-codec-http:${versions.nettyVersion}",
        'netty-codec-http2'                        : "io.netty:netty-codec-http2:${versions.nettyVersion}",
        'netty-common'                             : "io.netty:netty-common:${versions.nettyVersion}",
        'netty-buffer'                             : "io.netty:netty-buffer:${versions.nettyVersion}",
        'netty-resolver'                           : "io.netty:netty-resolver:${versions.nettyVersion}",
        'netty-transport'                          : "io.netty:netty-transport:${versions.nettyVersion}",
        'netty-transport-native-epoll'             : "io.netty:netty-transport-native-epoll:${versions.nettyVersion}",
        'netty-transport-native-unix-common'       : "io.netty:netty-transport-native-unix-common:${versions.nettyVersion}",
        'protobuf-java'                            : "com.google.protobuf:protobuf-java:${versions.protobufJavaVersion}",
        'spring-mock'                              : "org.springframework:spring-mock:${versions.springMockVersion}",
        'springfox-swagger-ui'                     : "io.springfox:springfox-swagger-ui:${versions.swaggerVersion}",
        'springfox-swagger2'                       : "io.springfox:springfox-swagger2:${versions.swaggerVersion}",
        'sqlite4java'                              : "com.almworks.sqlite4java:sqlite4java:${versions.sqlite4javaVersion}",
        'sqlite4java-win32-x64'                    : "com.almworks.sqlite4java:sqlite4java-win32-x64:${versions.sqlite4javaVersion}",
        'sqlite4java-win32-x86'                    : "com.almworks.sqlite4java:sqlite4java-win32-x86:${versions.sqlite4javaVersion}",
        'spring-cloud-function-adapter-aws'        : "org.springframework.cloud:spring-cloud-function-adapter-aws:${versions.springContextVersion}",
        'spring-cloud-function-kotlin'             : "org.springframework.cloud:spring-cloud-function-kotlin:${versions.springContextVersion}",
        'spring-cloud-function-context'            : "org.springframework.cloud:spring-cloud-function-context:${versions.springContextVersion}",
        'spring-cloud-function-core'               : "org.springframework.cloud:spring-cloud-function-core:${versions.springContextVersion}",
        'spring-cloud-function-web'                : "org.springframework.cloud:spring-cloud-function-web:${versions.springContextVersion}",
        'spring-cloud-starter-function-web'        : "org.springframework.cloud:spring-cloud-starter-function-web:${versions.springContextVersion}",
        'spring-cloud-starter-stream-kafka'        : "org.springframework.cloud:spring-cloud-starter-stream-kafka:${versions.springCloudKafkaVersion}",
        'spring-cloud-starter-stream'              : "org.springframework.cloud:spring-cloud-stream:${versions.springCloudKafkaVersion}",
        'spring-cloud-starter-binder'              : "org.springframework.cloud:spring-cloud-stream-binder-kafka:${versions.springCloudKafkaVersion}",
        'spring-cloud-starter-binder-core'         : "org.springframework.cloud:spring-cloud-stream-binder-kafka-core:${versions.springCloudKafkaVersion}",
        'kotlin-stdlib-common'                     : "org.jetbrains.kotlin:kotlin-stdlib-common:${versions.kotlinjdk}",
        'kotlin-scripting-compiler'                : "org.jetbrains.kotlin:kotlin-scripting-compiler-embeddable:${versions.kotlinjdk}",
        'kotlin-compiler-embeddable'               : "org.jetbrains.kotlin:kotlin-compiler-embeddable:${versions.kotlinjdk}",
        'kotlin-annotation-processing'             : "org.jetbrains.kotlin:kotlin-annotation-processing-gradle:${versions.kotlinjdk}",
        'kotlin-klib-commonizer'                   : "org.jetbrains.kotlin:kotlin-klib-commonizer-embeddable:${versions.kotlinjdk}",
        'kotlin-scripting-common'                  : "org.jetbrains.kotlin:kotlin-scripting-common:${versions.kotlinjdk}",
        'kotlin-stdlib-jdk7'                       : "org.jetbrains.kotlin:kotlin-stdlib-jdk7:${versions.kotlinjdk}",
        'typesafe-config'                          : "com.typesafe:config:${versions.typesafeConfigVersion}",
        'validation-api'                           : "javax.validation:validation-api:${versions.validationApiVersion}",
        'wiremock-jre8'                            : "com.github.tomakehurst:wiremock-jre8:${versions.wiremockVersion}",
        'woodstox-core'                            : "com.fasterxml.woodstox:woodstox-core:${versions.woodstoxVersion}",
        'mockk'                                    : "io.mockk:mockk:${versions.mockk}",
        'snakeyaml'                                : "org.yaml:snakeyaml:${versions.snakeYamlVersion}"
]