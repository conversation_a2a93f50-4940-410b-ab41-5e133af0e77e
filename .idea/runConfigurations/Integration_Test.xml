<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Integration Test" type="JUnit" factoryName="JUnit">
    <module name="nova-acc-offer-consumer.nova-acc-offer-consumer-test.main" />
    <useClassPathOnly />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
    <option name="PACKAGE_NAME" value="com.loyalty.nova.acc.offer.consumer.test" />
    <option name="MAIN_CLASS_NAME" value="com.loyalty.nova.acc.offer.consumer.test.ConsumerIT" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="class" />
    <option name="VM_PARAMETERS" value="-ea -Dcucumber.options=&quot;--tags @local&quot; -Dspring.profiles.active=local -Djmeter.home=src/main/resources/jmeter-home" />
    <option name="PARAMETERS" value="" />
    <envs>
      <env name="AWS_ACCESS_KEY_ID" value="applmgr" />
      <env name="AWS_DEFAULT_REGION" value="us-east-1" />
      <env name="AWS_SECRET_ACCESS_KEY" value="applmgr" />
    </envs>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>