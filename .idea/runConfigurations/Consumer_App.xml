<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Consumer App" type="JetRunConfigurationType">
    <module name="nova-acc-offer-consumer.nova-acc-offer-consumer-service.main" />
    <option name="VM_PARAMETERS" value="-Dspring.profiles.active=local" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="ALTERNATIVE_JRE_PATH" value="11" />
    <option name="PASS_PARENT_ENVS" value="true" />
    <option name="MAIN_CLASS_NAME" value="com.loyalty.nova.acc.offer.ConsumerApp" />
    <option name="WORKING_DIRECTORY" value="" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>