<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Unit Test" type="JUnit" factoryName="JUnit">
    <module name="nova-acc-offer-consumer.nova-acc-offer-consumer-service.test" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.loyalty.nova.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <extension name="net.ashald.envfile">
      <option name="IS_ENABLED" value="false" />
      <option name="IS_SUBST" value="false" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
      <option name="IS_IGNORE_MISSING_FILES" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" />
      </ENTRIES>
    </extension>
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
    <option name="PACKAGE_NAME" value="com.loyalty.nova" />
    <option name="MAIN_CLASS_NAME" value="" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="tags" />
    <option name="VM_PARAMETERS" value="-ea -Djava.library.path=build/libs -Dspring.profiles.active=local" />
    <option name="PARAMETERS" value="" />
    <envs>
      <env name="ENVIRONMENT" value="local" />
    </envs>
    <tag value="unit" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>