<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="LocalWiremockServerKt" type="JetRunConfigurationType" nameIsGenerated="true">
    <module name="nova-acc-offer-consumer.nova-acc-offer-consumer-test.main" />
    <option name="VM_PARAMETERS" value="" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
    <option name="ALTERNATIVE_JRE_PATH" />
    <option name="PASS_PARENT_ENVS" value="true" />
    <option name="MAIN_CLASS_NAME" value="com.loyalty.nova.acc.offer.consumer.test.mocks.LocalWiremockServerKt" />
    <option name="WORKING_DIRECTORY" value="" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>