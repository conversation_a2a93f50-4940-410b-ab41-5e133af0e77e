// Jenkins deployment pipeline
pipeline {
    agent none
    stages {
        stage('Deploying') {
            agent {
                node {
                    label 'aws-ec2'
                }
            }
            steps {
                script{
                    //ensure meta.config is populated for hotfix to run
                    def appMeta = readProperties(file: 'meta.config')
                    def repository = gitUtils.getRepoName()
                    //refer to Jenkins Shared Library regarding these functions
                    //https://github.com/LoyaltyOne/jenkins-shared-lib-v2 
                    awsUtils.withDeployer(params.DEST_ENVIRONMENT, appMeta.AWS_DEST_ACCOUNT){
                        echo "Deploying to ${DEST_ENVIRONMENT}-${repository} in ${appMeta.AWS_DEST_ACCOUNT} account"
                        def cmd = """ecs-service deploy ${DEST_ENVIRONMENT}-${repository} ${gitTag} aws/cfn/service.json env/${DEST_ENVIRONMENT}.params.json -e env/${DEST_ENVIRONMENT}.env -t env/${DEST_ENVIRONMENT}.tags.json -r us-east-1"""
                        //execute command in AWS/execute in Jenkinstein
                        awsUtils.execute(params.DEST_ENVIRONMENT, appMeta.AWS_DEST_ACCOUNT, repository, params.gitTag, cmd)  
                    }
                }

            }
        }
    }
}