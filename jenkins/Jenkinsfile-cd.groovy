@Library('jenkins-shared-lib-v2') _

def blue = "#42b3f4"
def good = "#3dd62f"
def danger = "#f45641"
def warning = "#ffd344"

jenkinsUtils = null

void importFunctions() {
  jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
}

pipeline {
  agent none

  stages {
    stage('Deploying to Dev.') {
      agent {
        docker {
          image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
        }
      }
      steps {
        println "Deploying to Dev."
        println "BUILD_VERSION: ${params.BUILD_VERSION}"
        importFunctions()
        withCredentials([[$class: "AmazonWebServicesCredentialsBinding", credentialsId: "dev-amrpwl-aws-deployer"]]) {
          script {
            //sh(script: './gradlew build')
            jfrog.credentials() {
              sh """
                   ./gradlew build -DjfrogUser="${ARTIFACTORY_USERNAME}" -DjfrogPassword="${ARTIFACTORY_PASSWORD}"
              """
            }
            jenkinsUtils.deployAccSecretStack('dev')
            jenkinsUtils.deployAccConsumerStack('dev', params.BUILD_VERSION, 'dev-amrpwl-lambda-functions-archive')
          }
        }
      }
      post {
        success {
          println 'success! Deployed to Dev.'
        }
        failure {
          println "failed to deploy to Dev."
        }
        aborted {
          println "job aborted. Did not deploy to Dev."
        }
      }
    }
//
//    stage("Dev Test") {
//      agent {
//        node {
//          label 'ubuntu-18'
//        }
//      }
//      steps {
//        println "Dev Test"
//        withCredentials([[
//                                 $class       : "AmazonWebServicesCredentialsBinding",
//                                 credentialsId: "dev-amrpwl-aws-deployer"
//                         ]]) {
//          sh(script: './gradlew integrationTest -PcucumberTag=@dev -Pprofiles=dev')
//        }
//      }
//      post {
//        always {
//          cucumber buildStatus: null,
//                  fileIncludePattern: '**/build/reports/cucumber/dev-cucumber-json-report.json',
//                  trendsLimit: 10
//        }
//        failure {
//          echo "Failed Dev Test"
//        }
//      }
//    }

    stage('Deploy to UAT?') {
      agent none
      steps {
        input(message: "Do you want to deploy version ${params.BUILD_VERSION} to UAT?")
      }
      post {
        success {
          echo 'Attempting to deploy to uat'
        }
        aborted {
          echo "Aborting attempt to deploy to uat"
        }
      }
    }

    stage('Deploying to UAT') {
      agent {
        docker {
          image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
        }
      }
      steps {
        println "Deploying to UAT"
        println "BUILD_VERSION: ${params.BUILD_VERSION}"
        importFunctions()
        withCredentials([[$class: "AmazonWebServicesCredentialsBinding", credentialsId: "uat-amrpwl-aws-deployer"]]) {
          script {
            jfrog.credentials() {
              sh """
                  ./gradlew build -DjfrogUser="${ARTIFACTORY_USERNAME}" -DjfrogPassword="${ARTIFACTORY_PASSWORD}"
              """
            }
            // sh(script: './gradlew build')
            jenkinsUtils.deployAccSecretStack('uat')
            jenkinsUtils.deployAccConsumerStack('uat', params.BUILD_VERSION, 'uat-amrpwl-lambda-functions-archive')
          }
        }
      }
    }
  }
}

