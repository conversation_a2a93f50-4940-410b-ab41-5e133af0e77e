@Library('jenkins-shared-lib-v2') _

def repo = "nova-acc-offer-consumer"

config = {}
jenkinsUtils = null

pipeline {
    agent { label 'aws-ec2' }
    stages {
        stage("Build") {

            // agent {
            // docker {
            // image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
            //  }
            //   }

            steps {
                println "Calling Utils"
                script {
                    jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
                }
                println "Build"
                script {
                    jfrog.credentials() {
                        sh """
                            ./gradlew clean build -DjfrogUser="${ARTIFACTORY_USERNAME}" -DjfrogPassword="${ARTIFACTORY_PASSWORD}"
                        """
                    }
                    sh(script: './gradlew dependencies')
                    sh(script: './gradlew cleanTest test jacocoTestReport --no-build-cache')
                    stash includes: 'nova-acc-offer-consumer-service/build/**,nova-acc-offer-consumer-test/build/**', name: 'build'
                }

                script {
                    awsUtils.withDeployer("nonprod", "amrpwl") {
                        sh(script: './jenkins/validate-cfn')
                    }
                    config.versionSnapshot = sh(script: './gradlew properties -q | grep ^version: | awk \'{print $2}\'', returnStdout: true)?.trim()
                    def (versionNumber, snapshot) = config.versionSnapshot.tokenize('-')
                    config.version = versionNumber
                }
            }

            // code for coverages part
            post {
                always {
                    script {
                        gitUtils.reportPRStatus()
                    }
                    junit '**/build/test-results/**/*.xml'
                    publishHTML(target: [
                            allowMissing: true, alwaysLinkToLastBuild: false, keepAll: true,
                            reportDir   : 'build/',
                            reportFiles : 'index.html',
                            reportTitles: 'Unit Tests',
                            reportName  : 'Unit Tests'])

                    sh './gradlew jacocoTestReport'
                    publishHTML(target: [
                            allowMissing: true, alwaysLinkToLastBuild: false, keepAll: true,
                            reportDir   : 'nova-acc-offer-consumer-service/build/reports/jacoco/',
                            reportFiles : 'index.html',
                            reportTitles: 'Service Module Code Coverage',
                            reportName  : 'Service Module Code Coverage'])

                }
            }
        }

        stage ('run checkmarx incremental scan') {
            agent { label 'aws-ec2' }
            when { 
                expression { config.version }
            }
            steps {
                script {
                checkmarx.scan(comment: config.version, incremental: true)
                }
            }
            post {
                cleanup {
                deleteDir()
                dir("${workspace}@tmp") {
                    deleteDir()
                }
                dir("${workspace}@script") {
                    deleteDir()
                }
                }      
            }
        }

        //calling sonar qube function
        stage ('Scan') {
            agent {
                docker {
                    image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22-slim'
                }
            }
            steps {
                script {
                    unstash 'build'
                    sonarqube.checkQualityGate()
                }
            }
        }

        stage("Sole Deploy") {
            when {
                expression { gitUtils.isReleasable() }
            }
            steps {
                println "Sole Deploy"
                script {
                    awsUtils.withDeployer("nonprod", "amrpwl") {
                        script {
                            jenkinsUtils.deployAccSecretStack('sole')
                            jenkinsUtils.deployAccConsumerStack('sole', config.versionSnapshot, 'sole-amrpwl-lambda-functions-archive')
                        }
                    }
                }
            }
        }

        //stage("Sole Test") {
        //when {
        //expression { gitUtils.isReleasable() }
        //}
        //steps {
        //println "Sole Test"
        //script {
        //awsUtils.withDeployer(params.env, params.account) {
        //sh(script: './gradlew integrationTest -PcucumberTag=@sole -Pprofiles=sole')
        //}
        //}
        //}
        //post {
        //always {
        //cucumber buildStatus: null,
        //fileIncludePattern: '**/build/reports/cucumber/sole-cucumber-json-report.json',
        //trendsLimit: 10
        //}
        //failure {
        //echo "Failed Sole Test"
        //script { notification.sendSlackMessage("things-dev", "Testing ${repo} failed") }
        //}
        //}
        //}

        stage("Release") {
            when {
                expression { gitUtils.isReleasable() }
            }
            steps {
                println "Tag Release"
                git(
                        url: "**************:AirMilesLoyaltyInc/${repo}.git",
                        branch: "master",
                        credentialsId: "jenkins-ssh-key"
                )
                sshagent(credentials: ['jenkins-ssh-key'], ignoreMissing: false) {
                    sh(script: './gradlew release -Prelease.useAutomaticVersion=true')
                }
            }
            post {
                success {
                    echo 'success! Lets start up the deployment job.'
                    build job: "Deployment/${repo}", wait: false,
                        parameters: [
                            string(name: 'BUILD_VERSION', value: config.version)
                        ]
                }
                failure {
                    script {
                        notification.sendSlackMessage("things-dev", "Failure on Tag Release for ${repo}")
                    }
                }
                aborted {
                    echo "job aborted."
                }
            }
        }
    }
}





