/**
 * Hosts the common utility functions for jenkins pipelines
 */

void deployAccConsumerStack(String env, String version, String bucketName) {
    def aggregateName = "${env}-nova-acc-offer-consumer"
    def stackName = "${aggregateName}-lambda"
    def moduleName = "nova-acc-offer-consumer-service"
    def templateFile = "consumer-template.yaml"

    echo "Deploying ${stackName}"

    def template = readYaml file: "aws/cfn/templates/${templateFile}"
    template.Resources.AccOfferConsumerLambda.Properties.CodeUri = "./../${moduleName}/build/libs/${moduleName}-${version}-aws.jar"
    writeYaml file: "build/${env}-${templateFile}", data: template

    def parameterOverrides = paramsFromKeyValuePairsFromFile("aws/cfn/${env}.params.json")
    def tags = paramsFromKeyValuePairsFromFile("aws/cfn/${env}.tags.json")
    def argsPackage = "package --template-file build/${env}-${templateFile} --s3-bucket ${bucketName} --s3-prefix sam/${stackName}-${version} --output-template-file build/packaged-${env}-${templateFile}"
    def argsDeploy = "deploy --template-file build/packaged-${env}-${templateFile} --region us-east-1 --stack-name ${stackName} --parameter-overrides ${parameterOverrides}  --tags ${tags} --capabilities CAPABILITY_NAMED_IAM --no-fail-on-empty-changeset"

    sh "aws cloudformation ${argsPackage}"
    sh "aws cloudformation ${argsDeploy}"
    sh "aws cloudformation detect-stack-drift --stack-name ${stackName} --region us-east-1"
}

void deployAccSecretStack(String env) {
    def aggregateName = "${env}-nova-acc-offer-consumer"
    def stackName = "${aggregateName}-secrets"
    def templateName = "aws/cfn/templates/secrets-template.yaml"

    echo "Deploying ${stackName}"
    deployResource(env, aggregateName, stackName, templateName)
}

void deployResource(String env, String aggregateName, String stackName, String templateName) {
    def tags = paramsFromKeyValuePairsFromFile("aws/cfn/${env}.tags.json")
    def parameterOverrides = paramsFromKeyValuePairsFromFile("aws/cfn/${env}.params.json")
    def args = "deploy --stack-name ${stackName} --region us-east-1 --template-file ${templateName} --parameter-overrides ${parameterOverrides} --tags ${tags} --capabilities CAPABILITY_IAM --no-fail-on-empty-changeset"

    sh "aws cloudformation ${args} --tags ${tags}"
    sh "aws cloudformation update-termination-protection --enable-termination-protection --stack-name ${stackName} --region us-east-1"
    sh "aws cloudformation detect-stack-drift --stack-name ${stackName} --region us-east-1"
}

def paramsFromKeyValuePairsFromFile(String filename) {
    def paramsJson = readJSON(file: filename)
    paramsJson.collect {
        item -> "${item}"
    }.join(" ")
}

return this
