@Library('jenkins-shared-lib-v2') _
// you can check available examples and pipelines at 
// https://github.com/LoyaltyOne/jenkins-shared-lib-v2 or other starter kits
//
// https://github.com/LoyaltyOne/starter-kit-express-api/blob/master/jenkins/Jenkinsfile-scheduled-cd.groovy
// https://github.com/LoyaltyOne/starter-kit-springboot-api/blob/master/jenkins/Jenkinsfile-scheduled-cd.groovy

pipeline {
  agent { label 'aws-ec2' }
  stages {
    stage ('SCHEDULED-DEPLOY') {
      steps {
        script{
          //Deploy Stack
          sh 'echo deploy stack'
        }
      }
    }
    stage('LOAD TESTING') {
      agent { node { label 'aws-ec2' } }
      steps {
        script{
          //testing
          sh 'echo test' 
        }
      }
    }
    stage('SCHEDULED-DELETE') {
      agent { node { label 'aws-ec2' } }
      steps {
        script{
          //Delete Stack
          sh 'echo delete stack'
        }
      }
    }
  }
}
