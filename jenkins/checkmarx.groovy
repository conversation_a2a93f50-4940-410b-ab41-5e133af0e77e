import groovy.json.JsonSlurperClassic
import groovy.json.JsonOutput

// REST API methods
enum Methods{
  GET,
  POST,
  PUT
}

/**
 * Retrieves a JWT Access Token from the Checkmarx API
 * <p>
 * <b>Example:</b> checkmarx.getToken()
 *
 * @return String JWT
 */
def getToken() {
  withCredentials([
    usernamePassword(credentialsId: 'checkmarx-admin', usernameVariable: 'USER', passwordVariable: 'PASS')
  ]) {

    def json;
    jenkinsUtils.maskExecute([PASS]) {
      json = sh(
        script: "curl --location --request POST 'https://checkmarx.loyalty.com/cxrestapi/auth/identity/connect/token' \
                        --header 'Content-Type: application/x-www-form-urlencoded' \
                        --data-urlencode 'username=${USER}' \
                        --data-urlencode 'password=${PASS}' \
                        --data-urlencode 'grant_type=password' \
                        --data-urlencode 'scope=sast_rest_api' \
                        --data-urlencode 'client_id=resource_owner_client' \
                        --data-urlencode 'client_secret=014DF517-39D1-4453-B7B3-9930C563627C'",
        returnStdout: true
      ).trim()
    }
    return new JsonSlurperClassic().parseText(json).get("access_token")
  }
}

/** 
 * This is a wrapper method to assist with making REST calls to the Checkmarx API. 
 * This method is not intended to be called from a Jenkinsfile 
 *
 * @param method Relative path of file with resources as root directory
 *                           i.e. "org/l1/test.json"
 *
 * @param endpoint A Github repository name. 
 *                             i.e. "jenkins-shared-pipeline-library"
 *
 * @return this should return the JSON response of the curl command
 **/
def callAPI(Methods method, String endpoint) {
  def token = getToken()
  def result;
  jenkinsUtils.maskExecute([token]) {
    result = sh(
      script: "curl --location --request ${method.name()} 'https://checkmarx.loyalty.com/cxrestapi/${endpoint}' \
                    --header 'Authorization: Bearer ${token}'",
      returnStdout: true
    ).trim()
  }
  return new JsonSlurperClassic().parseText(result)
}

/** 
 * Retrieves the Checkmarx Group Id of a Github Repository that was generated from a 
 * Starterkit. 
 * <p>
 * <b>Example:</b> checkmarx.getGroupId()
 *
 * @param teamToCheck A team name to check it's groupID 
 * @return Map => if successful, return java.util.Map object:
 *                  {
 *                         "status": true,
 *                         "data": "<Group_ID_HERE>"
 *                  }
 *                if unsuccessful, return java.util.Map object:
 *                  {
 *                        "status": false,
 *                        "data": "<CHECKMARX_TEAM_NAME> is not available inside of checkmarx."
 *                  }
 */
def getGroupId(String teamToCheck){
  def teams = callAPI(Methods.GET, "/auth/teams/")
  
  println "Team to check: ${teamToCheck}"
  println "Teams: ${teams}"
  
  def status = false
  def name 
  def data = "${teamToCheck} is not available inside of checkmarx, reach out to security team to create new team on checkmarx and setup project"
  teams.each {
    if (it.fullName.contains(teamToCheck)) {
      name= it.fullName.split("/")[4] 
      if (name.equalsIgnoreCase(teamToCheck)) {
        status = true
        data = it.id
      }
    }
  }
  
  return [
    "status": status,
    "data": data,
  ]
}

/** 
* Get a list of Loyalty One team names filtered from Checkmarx.callAPI(method, 
* endpoint, payload)
* <p>
* <b>Example:</b> checkmarx.getTeams()

* @return if successful, return String JSON object: 
*                                '{
*                                       "status": true,
*                                       "data": [
*                                                    "TeamName1",
*                                                    "TeamName2",
*                                                    "TeamName3"
*                                       ]
*                                 }'
*         if unsuccessful, return String JSON object:
*                                 '{
*                                       "status": false,
*                                       "data": "Checkmarx API Connectivity Error."
*                                 }'
*
*/
def getTeams(){
  def teams
  try {
    teams = callAPI(Methods.GET, "/auth/teams/")
  }
  catch(Exception error){
    return JsonOutput.toJson([
      "status": false,
      "data": error.getMessage(),
    ])
  }
 
  println "Teams: ${teams}"
  
  def fullNames = teams.collect{ it.fullName }
  println fullNames
  
  return JsonOutput.toJson([
    "status": true,
    "data": fullNames,
  ])
}

/** 
 * Retrieves the Checkmarx Project Id of a Github Repository in Checkmarx
 * It checks if the project name and team name matches with meta.config of a project repository
 * <p>
 * <b>Example:</b> checkmarx.getProjectId('test-project', 'Team_Dynamite')
 *
 * @param projectToCheck A project name to check it's projectID 
 * @param teamToCheck A team name to check if project exists under this team
 * @return Map => if successful, return java.util.Map object:
 *                  {
 *                         "projectId": "<Project_ID_HERE>",
 *                         "projectExists": false
 *                  }
 *                if unsuccessful, return java.util.Map object:
 *                  {
 *                        "projectId": null
 *                        "projectExists": "project: test-project exists under a different team, please check with security team"
 *                  }
 */
def getProjectId(String projectToCheck, String teamToCheck){
    def groupId = getGroupId(teamToCheck).data
    def projects = callAPI(Methods.GET, "/projects/")
    println "Project to check: ${projectToCheck}"
    println "Team to check: ${teamToCheck}"
    println "Group ID: $groupId"
    def status = false
    def projectId = null
    def projectExists = false
    projects.each {
        if (it.name == projectToCheck && it.teamId == groupId) {
            status = true
            projectId = it.id
            groupId = it.teamId
            println "project: ${projectToCheck} with projectId: ${projectId} exists under team: ${teamToCheck} with teamId: ${groupId} and matches with meta.config"
        }
        else {
            if (it.name == projectToCheck && !it.teamId == groupId) {
                 "project: ${projectToCheck} exists under a different team, please check with security team"
                 projectExists = true
            }
        }
    }
    return [
        "projectId": projectId,
        "projectExists": projectExists,
    ]
}

/** 
 * Creates a project in Checkmarx if the project does not exists and return projectID
 * <p>
 * <b>Example:</b> checkmarx.createProject('test-project', 'Team_Dynamite')
 *
 * @param projectName A project name to create in Checkmarx
 * @param teamName A team name to check and create the project under it
 * @return String 'newProjectId', if the project exists on Checkmarx, then it will return 'null'
 */
def createProject(String projectName, String teamName){
    def token = getToken()
    def result;
    def newProjectId;
    def groupId = getGroupId(teamName).data as String
    def getProjectId = getProjectId(projectName, teamName)
    def projectId = getProjectId.projectId
    def projectExists = getProjectId.projectExists
    println "Checking project in checkmarx"
    if(projectId == null && !projectExists) {
        println "Project ${projectName} does not exists in checkmarx"
        println "Creating project ${projectName}"
        jenkinsUtils.maskExecute([token]) {
            result = sh(
                script: "curl --location --request POST 'https://checkmarx.loyalty.com/cxrestapi/projects' \
                                --header 'Authorization: Bearer ${token}' \
                                --data-urlencode 'name=${projectName}' \
                                --data-urlencode 'owningTeam=${groupId}' \
                                --data-urlencode 'isPublic=true'",
                returnStdout: true
            ).trim()
            println result
            newProjectId = new JsonSlurperClassic().parseText(result).get("id")
        }
        return newProjectId
    }
    else {
        return "project: ${projectName} with projectId: ${projectId} exists in checkmarx, skipping create project"
    }
}

/** 
 * Sets the excludeFolder config on a checkmarx project
 * It checks if the project name and team name matches with meta.config in project repository
 * and only sets values when the condition is met
 * <p>
 * <b>Example:</b> checkmarx.setExcludeFolder('test-project', 'jenkins, src/test')
 *
 * @param projectName A project name on which excludeFolders needs to be set
 * @param excludeFolders All the folders that should be excluded from checkmarx scan
 * @return String => if successful, return
 *
 *                 "Success: Setting excludeFolder on project: test-project completed, response code: 200"
 *
 *                if unsuccessful, return
 *
 *                 "Setting excludeFolder for the project: test-project failed, response code: 404"
 * It will print the below message if project exists in checkmarx but under a different team other than as defined in meta.config
 * Message => "The project: test-project is not in the right team: Team_Dynamite as per meta.config, skipping setting exclude folder(s)" 
 */
def setExcludeFolder(String projectName, String excludeFolders = 'jenkins, src/test'){
    def meta = readProperties(file: "meta.config")
    def token = getToken()
    def result;
    def status
    def teamName = meta.CHECKMARX_TEAM_NAME
    if (teamName == null) {
        error "CHECKMARX_TEAM_NAME is required, check meta.config file"
    }
    def groupId = getGroupId(teamName).data
    def projects = callAPI(Methods.GET, "/projects/")
    projects.each {
        if (!it.name==projectName) {
            error "The project: ${projectName} does not exists in checkmarx"
        }
        if ((it.name==projectName) && (it.teamId==groupId)) {
            projectId = it.id
            groupId = it.teamId
            println "project: ${projectName} with projectId: ${projectId} exists under team: ${teamName} with teamId: ${groupId} and matches with meta.config"
            println "setting excludeFolders: ${excludeFolders} on the project: ${projectName}"
            jenkinsUtils.maskExecute([token]) {
                result = sh(
                    script: """curl --location -w "%{http_code}" --request PUT 'https://checkmarx.loyalty.com/cxrestapi/projects/${projectId}/sourceCode/excludeSettings' \
                                    --header 'Accept: application/json;v=1.0' \
                                    --header 'Content-Type: application/x-www-form-urlencoded' \
                                    --header 'Authorization: Bearer ${token}' \
                                    --data-urlencode 'id=${projectId}' \
                                    --data-urlencode 'excludeFoldersPattern=${excludeFolders}'""",
                    returnStdout: true
                ).trim().toInteger()
            }
            if (result == 200) {
                status = "Success: Setting excludeFolder on project: ${projectName} completed, response code: ${result}"
            }             
            else {
                error "Setting excludeFolder for the project: ${projectName} failed, response code: ${result}"
            }
        }
        else if ((it.name==projectName) && !(it.teamId==groupId)) {
            println "The project: ${projectName} is not in the right team: ${teamName} as per meta.config, skipping setting exclude folder(s)"
        }
    }
    return status
}

/** 
 * Sets the location config (github url) on a checkmarx project
 * It checks if the project name and team name matches with meta.config in project repository
 * and only sets values when the condition is met
 * <p>
 * <b>Example:</b> checkmarx.setLocation('test-project', 'main')
 *
 * @param projectName A project name on which excludeFolders needs to be set
 * @param branch Branch to use while running the checkmarx scan
 * @return String => if successful, return
 *
 *                 "Success: Setting location on project: test-project completed, response code: 204"
 *
 *                if unsuccessful, return
 *
 *                 "Setting location for the project: test-project failed, response code: 400"
 * It will print the below message if project exists in checkmarx but under a different team other than as defined in meta.config
 * Message => "The project: test-project is not in the right team: Team_Dynamite as per meta.config, skipping setting location" 
 */
def setLocation(String projectName, String branch = 'main'){
    def meta = readProperties(file: 'meta.config')
    def token = getToken()
    def result;
    def status;
    def separatorString = "xxxxx-separator-xxxxx"
    def teamName = meta.CHECKMARX_TEAM_NAME
    if (teamName == null) {
        error "CHECKMARX_TEAM_NAME is required, check meta.config file"
    }
    def groupId = getGroupId(teamName).data
    def projects = callAPI(Methods.GET, "/projects/")
    projects.each {
        if (!it.name==projectName) {
            error "The project: ${projectName} does not exists in checkmarx"
        }
        if ((it.name==projectName) && (it.teamId==groupId)) {
            projectId = it.id
            println "project: ${projectName} with projectId: ${projectId} exists under team: ${teamName} with teamId: ${groupId} and matches with meta.config"
            jenkinsUtils.maskExecute([token]) {
                println "getting ssh key from jenkins credentials"
                withCredentials([
                    sshUserPrivateKey(
                    credentialsId: 'jenkins-ssh-key',
                    keyFileVariable: 'keyFile')
                ]) {
                    def sshKey = readFile(keyFile).trim()
                    println "setting github url, branch and ssh key on the project: ${projectName}"
                    result = sh(
                        // using "set +x" to hide the sshKey from printing on console
                        script: """set +x; curl -i -o /dev/null -w "%{http_code}" -s -X 'POST' \
                                    -H 'Accept: application/json;v=1.3' \
                                    -H 'Authorization: Bearer ${token}' \
                                    -H 'Content-Type: multipart/form-data; boundary=${separatorString}' \
                                    --data-binary '--${separatorString}\r\nContent-Disposition: form-data; \
                                    name=\"url\"\r\n\r\<EMAIL>:AirMilesLoyaltyInc/${projectName}.git\r\n--${separatorString}\r\nContent-Disposition: form-data; \
                                    name=\"branch\"\r\n\r\n/refs/heads/${branch}\r\n--${separatorString}\r\nContent-Disposition: form-data; \
                                    name=\"privateKey\"; filename=\"privateKey\"\r\n\r\n${sshKey}\n\r\n--${separatorString}--\r\n' \
                                    'https://checkmarx.loyalty.com/cxrestapi/help/projects/${projectId}/sourceCode/remoteSettings/git/ssh'""",
                        returnStdout: true
                    ).toInteger()
                }
                if (result == 204) {
                    status = "Success: Setting location on project: ${projectName} completed, response code: ${result}"
                }             
                else {
                    error "Setting location for the project: ${projectName} failed, response code: ${result}"
                }
            }
        }
        else if ((it.name==projectName) && !(it.teamId==groupId)) {
            println "The project: ${projectName} is not in the right team: ${teamName} as per meta.config, skipping setting location"
        }
    }
    return status
}

/** 
 * Sets a new project on checkmarx
 * It checks if the project name and team name matches with meta.config in project repository
 * and only sets a new project when the condition is met
 * It calls 3 functions - createProject, setExcludeFolder, setLocation
 * <p>
 * <b>Example:</b> checkmarx.setProject('test-project', 'Team_Dynamite', 'main', 'jenkins, src/test')
 *
 * @param projectName A project name on which excludeFolders needs to be set
 * @param teamName A team name to check and create the project under it
 * @param branch Branch to use while running the checkmarx scan
 * @param excludeFolders All the folders that should be excluded from checkmarx scan
 * @return The return is just printed from the sub functions that it calls
 */
def setProject(String projectName, String teamName, String branch = 'main', String excludeFolders = 'jenkins, src/test'){

    println "projectName: ${projectName}"
    println "teamName: ${teamName}"
    println "branch: ${branch}"
    println "excludeFolders: ${excludeFolders}"
    gitUtils.clone(projectName)
    // using dir() function as we need to call all following functions under the repo directory to read meta.config
    dir(projectName) {
      println "executing function createProject()"
      def createProject = createProject(projectName, teamName)
      println createProject

      println "executing function setExcludeFolder()"
      def setExcludeFolder = setExcludeFolder(projectName, excludeFolders)
      println setExcludeFolder

      println "executing function setLocation()"
      def setLocation = setLocation(projectName, branch)
      println setLocation
    }
}

/**
 *
 * Runs a checkmarx scan on a project's source code.
 * CI pipelines that call this function will push their scans to their corresponding team's and project as indicated by meta.config  
 * <p>
 * <pre>
 * <b>Note:</b> The expectation is that the project already exists in checkmarx, this function does not create project if it does not exists
 * Please create the project manually from checkmarx UI or check with security team
 * <b>Warning:</b> Please NEVER invoke the teamName and/or projectName parameters directly in your Jenkinsfiles EVER
 * <b>Example of WHAT NOT TO DO:</b> checkmarx.scan( projectName: 'myapp1', teamName: 'Team_1' )
 * <b>Example 1:</b> checkmarx.scan()
 * <b>Example 2:</b> 
 *      checkmarx.scan(excludeFolders: 'a/b/dir1, dir2', comment: 'this is a test scan')
 * </pre>
 * 
 * @param Map[Key:Value] A dictionary containing the exposed parameters of the Jenkins Checkmarx Plugin
 * @param projectName A project name on which the scan needs to be triggered, default value is set to check meta.config
 * @param teamName A team name that the project is associated with, default value is set to check meta.config
 * @param comment A comment to add to the scan, default value is null
 * @param excludeFolders All the folders that should be excluded from checkmarx scan, this value will be set on the project
 * @return The return is the scan id from the checkmarx
 *
 */
def scan(Map stepParams = [:]){
  def token = getToken()
  def result;
  def meta = readProperties(file: 'meta.config')
  
  def teamName = stepParams.teamName ? stepParams.teamName : meta.CHECKMARX_TEAM_NAME
  def projectName = stepParams.projectName ? stepParams.projectName : meta.APP_ID
  def comment = stepParams.comment ? stepParams.comment : null
  def excludeFolders = stepParams.excludeFolders ? stepParams.excludeFolders : null
  def incremental = stepParams.incremental ? stepParams.incremental : false
  
  println "stepPamrams.incremental is " + stepParams.incremental
  println "Job Name: ${env.JOB_NAME}"
  println "Team: ${teamName}"
  println "Project: ${projectName}"
  println "incremental: ${incremental}"
  println "Comment: ${comment}"
  println "excludeFolders: ${excludeFolders}" 

  if(excludeFolders != null) {
    println "executing function setExcludeFolder()"
    def setExcludeFolder = setExcludeFolder(projectName, excludeFolders)
    println setExcludeFolder
  }

  def getProjectId = getProjectId(projectName, teamName)
  def projectId = getProjectId.projectId
    jenkinsUtils.maskExecute([token]) {
        result = sh(
            script: "curl --location --request POST 'https://checkmarx.loyalty.com/cxrestapi/sast/scans' \
                            --header 'Authorization: Bearer ${token}' \
                            --data-urlencode 'projectId=${projectId}' \
                            --data-urlencode 'isIncremental=${incremental}' \
                            --data-urlencode 'isPublic=true' \
                            --data-urlencode 'forceScan=true' \
                            --data-urlencode 'comment=${comment}'",
            returnStdout: true
        ).trim()
        println result
    }
    def scanId = new JsonSlurperClassic().parseText(result).get("id")
    println "scanId is " + scanId
//  return new JsonSlurperClassic().parseText(result)
    return scanId
}

/**
 *
 * Get a checkmarx scan status.
 * @param scanId is the result from calling scan() function
 * @return return the status of checkmarx scan
 *
 */
def getSingelScanStatus(scanId){
  def token = getToken()
  
  def result
  def scanStatus = ''
  def json

  jenkinsUtils.maskExecute([token]) {
    while (scanStatus != 'Finished'){
    	sleep 5
      result = sh(
	      script: "curl --location --request GET 'https://checkmarx.loyalty.com/cxrestapi/sast/scansQueue/${scanId}' \
	                    --header 'Authorization: Bearer ${token}' ",
	        returnStdout: true
	      ).trim()
	    json = new JsonSlurperClassic().parseText(result)
	    scanStatus = json.stage.value
//	    println result
	    println "scan status: " + scanStatus 
    } 
  }
  return scanStatus
}


/**
 *
 * Get a checkmarx scan statistics.
 * @param scanId is the result from calling scan() function
 * @return return the statistics of checkmarx scan, such as how many high/midium/low vulnerebilities
 *
 */
def getResultsStatistics(scanId){
  def token = getToken()  
  def result
  def json
  
  jenkinsUtils.maskExecute([token]) {
    result = sh(
      script: "curl --location --request GET 'https://checkmarx.loyalty.com/cxrestapi/sast/scans/${scanId}/resultsStatistics' \
                    --header 'Authorization: Bearer ${token}' ",
        returnStdout: true
      ).trim()
      println result
    }
    json = new JsonSlurperClassic().parseText(result)

	  return [
			"highSeverity": json.highSeverity,
			"mediumSeverity": json.mediumSeverity,
			"lowSeverity": json.lowSeverity
	  ]
}

/**
 *
 * Runs a checkmarx full scan on a project's source code.
 * CI pipelines that call this function will push their scans to their corresponding team's and project as indicated by meta.config  
 * <p>
 * <pre>
 * <b>Note:</b> The expectation is that the project already exists in checkmarx, this function does not create project if it does not exists
 * Please create the project manually from checkmarx UI or check with security team
 * <b>Warning:</b> Please NEVER invoke the teamName and/or projectName parameters directly in your Jenkinsfiles EVER
 * <b>Example of WHAT NOT TO DO:</b> checkmarx.scanWithGate( projectName: 'myapp1', teamName: 'Team_1' )
 * <b>Example 1:</b> checkmarx.scanWithGate()
 * <b>Example 2:</b> 
 *      checkmarx.scan(excludeFolders: 'a/b/dir1, dir2', comment: ReleaseVersion, highThreshold: 1)
 * </pre>
 * 
 * @param Map[Key:Value] A dictionary containing the exposed parameters of the Jenkins Checkmarx Plugin
 * @param high Threshhold for high vulnerebilities, default value is 1
 * @param medium Threshhold for medium vulnerebilities, default value is null
 * @param low Threshhold for low vulnerebilities, default value is null 
 *
 * @param projectName A project name on which the scan needs to be triggered, default value is set to check meta.config
 * @param teamName A team name that the project is associated with, default value is set to check meta.config 
 * @param comment A comment to add to the scan, default value is null
 * @param excludeFolders All the folders that should be excluded from checkmarx scan, this value will be set on the project
 * @return The return is the scan id from the checkmarx
 *
 */
def scanWithGate(Map stepParams = [:]){
  def high = stepParams.highThreshold ? stepParams.highThreshold : 1
  def medium = stepParams.mediumThreshold ? stepParams.mediumThreshold : null
  def low = stepParams.lowThreshold ? stepParams.lowThreshold : null
  
  def meta = readProperties(file: 'meta.config')
  def teamName = stepParams.teamName ? stepParams.teamName : meta.CHECKMARX_TEAM_NAME
  def projectName = stepParams.projectName ? stepParams.projectName : meta.APP_ID
  def excludeFolders = stepParams.excludeFolders ? stepParams.excludeFolders : null
  def comment = stepParams.comment ? stepParams.comment : null
  
  def scanParams = [
  			"teamName": teamName,
  			"projectName": projectName,
  			"excludeFolders": excludeFolders,
  			"comment": comment,
  			"incremental": false
  		] 
  def scanId = scan(scanParams)
  getSingelScanStatus(scanId)   
  def scanStatistics = getResultsStatistics(scanId)
  
  
  if ((medium != null) && (low != null )){            //Threshhold for high/medium/low
	  if ((scanStatistics.highSeverity >= high ) || (scanStatistics.mediumSeverity >= medium ) || (scanStatistics.lowSeverity >= low )){
	    error "Scan result for high/medium/low vulnerebilities are high than threshold. Please check on https://checkmarx.loyalty.com/ for details."
	  }
	}
	else if ((medium != null) && (low == null )){            //Threshhold for high/medium
	  if ((scanStatistics.highSeverity >= high ) || (scanStatistics.mediumSeverity >= medium )){
	    error "Scan result for high/medium vulnerebilities are high than threshold. Please check on https://checkmarx.loyalty.com/ for details."
	  }
	}
	else if ((medium == null) && (low != null )){             //Threshhold for high/low
	  if ((scanStatistics.highSeverity >= high ) || (scanStatistics.lowSeverity >= low )){
	    error "Scan result for high/low vulnerebilities are high than threshold. Please check on https://checkmarx.loyalty.com/ for details."
	  }
	}
	else if (scanStatistics.highSeverity >= high ) {
	    error "Scan result for high vulnerebilities are high than threshold. Please check on https://checkmarx.loyalty.com/ for details."
	}
	else {
	  println "Checkmarx scan passed!"
	}
}


/**
 *
 * NOTE: DO NOT USE THIS FUNCTION - THIS HAS BEEN DEPRECATED AND A NEW FUNCTION CHECKMARX.SCAN() IS IN USE WHICH USES CHECKMARX API INSTEAD OF JENKINS PLUGIN
 * This function will be deleted in future.
 * Runs a checkmarx scan on a project's source code.
 * CI pipelines that call this function will push their scans to their corresponding team's and project as indicated by meta.config  
 * <p>
 * <pre>
 * <b>Warning:</b> Please NEVER invoke the dskTeamName and/or dskProjectName parameters directly in your Jenkinsfiles EVER
 * <b>Example of WHAT NOT TO DO:</b> checkmarx.scan( dskProjectName: 'myapp1', dskTeamName: 'Team_1' )
 * <b>Example 1:</b> checkmarx.scan( excludeFolders: 'a/b/dir1, dir2' )
 * <b>Example 2:</b> 
 *      checkmarx.scan(
 *          hideDebugLogs: true,
 *          excludeFolders: '',
 *          highThreshold: 1,
 *          mediumThreshold: null,
 *          lowThreshold: null,
 *          <b>most parameters from this <a href"https://www.jenkins.io/doc/pipeline/steps/checkmarx/">Checkmarx Plugin List</a> are available for use. Reach out to TD if it is not</b>
 *      )  
 * </pre>
 * 
 * @param Map[Key:Value] A dictionary containing the exposed parameters of the Jenkins Checkmarx Plugin
 *
 */

// def scan(Map stepParams = [:]){
//   def meta = readProperties(file: 'meta.config')

//   def isUsedByDSKJob = env.JOB_NAME == "_Shared/Developer Starter Kit (DSK)"
//   def dskTeamName = isUsedByDSKJob ? stepParams.dskTeamName : meta.CHECKMARX_TEAM_NAME
//   def dskProjectName = isUsedByDSKJob ? stepParams.dskProjectName : meta.APP_ID 
//   def dskGroupId = getGroupId(dskTeamName).data as String
//   def commentWithProjectName = isUsedByDSKJob ? "Created By DSK" : meta.APP_ID

//   println "Job Name: ${env.JOB_NAME}"
//   println "Team ID: ${dskTeamName}"
//   println "Project: ${dskProjectName}"

//   def defaultFilterPattern = '''
//       !**/_cvs/**/*, !**/.svn/**/*, !**/.hg/**/*,    !**/.git/**/*,  !**/.bzr/**/*,
//       !**/bin/**/*,  !**/obj/**/*,  !**/backup/**/*, !**/.idea/**/*, !**/*.DS_Store,
//       !**/*.ipr,     !**/*.iws,     !**/*.bak,       !**/*.tmp,      !**/*.aac,
//       !**/*.aif,     !**/*.iff,     !**/*.m3u,       !**/*.mid,      !**/*.mp3,
//       !**/*.mpa,     !**/*.ra,      !**/*.wav,       !**/*.wma,      !**/*.3g2,
//       !**/*.3gp,     !**/*.asf,     !**/*.asx,       !**/*.avi,      !**/*.flv,
//       !**/*.mov,     !**/*.mp4,     !**/*.mpg,       !**/*.rm,       !**/*.swf,
//       !**/*.vob,     !**/*.wmv,     !**/*.bmp,       !**/*.gif,      !**/*.jpg,
//       !**/*.png,     !**/*.psd,     !**/*.tif,       !**/*.swf,      !**/*.jar,
//       !**/*.zip,     !**/*.rar,     !**/*.exe,       !**/*.dll,      !**/*.pdb,
//       !**/*.7z,      !**/*.gz,      !**/*.tar.gz,    !**/*.tar,      !**/*.gz,
//       !**/*.ahtm,    !**/*.ahtml,   !**/*.fhtml,     !**/*.hdm,      !**/*.hdml,
//       !**/*.hsql,    !**/*.ht,      !**/*.hta,       !**/*.htc,      !**/*.htd,
//       !**/*.war,     !**/*.ear,     !**/*.htmls,     !**/*.ihtml,    !**/*.mht,
//       !**/*.mhtm,    !**/*.mhtml,   !**/*.ssi,       !**/*.stm,      !**/*.stml,
//       !**/*.ttml,    !**/*.txn,     !**/*.xhtm,      !**/*.xhtml,    !**/*.class,
//       !**/*.iml,     !Checkmarx/Reports/*.*
//     '''


//   stepParams.filterPattern = stepParams.filterPattern ? defaultFilterPattern + ', ' + stepParams.filterPattern : defaultFilterPattern

//   step([$class: 'CxScanBuilder',
//     // do not merge..chat with team first
//     // (repo meta.config)Developer Configurable Params
//     projectName: dskProjectName,
//     groupId: dskGroupId,
//     // Developer Configurable Params
//     avoidDuplicateProjectScans: false,
//     buildStep: stepParams.buildStep ?: '',
//     comment: commentWithProjectName,
//     configAsCode: true,
//     credentialsId: stepParams.credentialsId ?: '',
//     //Unimplemented
//     //dependencyScanConfig: [
//     //    dependencyScanExcludeFolders: '', 
//     //    dependencyScanPatterns: '', 
//     //    fsaVariables: '', 
//     //    osaArchiveIncludePatterns: '*.zip, *.war, *.ear, *.tgz', 
//     //    overrideGlobalConfig: true, 
//     //    sastCredentialsId: '', 
//     //    scaAccessControlUrl: 'https://platform.checkmarx.net', 
//     //    scaConfigFile: '', 
//     //    scaCredentialsId: '', 
//     //    scaEnvVariables: '', 
//     //    scaSASTProjectFullPath: '', 
//     //    scaSASTProjectID: '', 
//     //    scaSastServerUrl: '', 
//     //    scaServerUrl: 'https://api-sca.checkmarx.net', 
//     //    scaTenant: '', 
//     //    scaWebAppUrl: 'https://sca.checkmarx.net'
//     //],
//     enableProjectPolicyEnforcement: stepParams.enableProjectPolicyEnforcement ?: false,
//     excludeFolders: stepParams.excludeFolders ?: '',
//     excludeOpenSourceFolders: stepParams.excludeOpenSourceFolders ?: '',
//     exclusionsSetting: stepParams.exclusionsSetting ?: 'job',//'global'
//     failBuildOnNewResults: stepParams.failBuildOnNewResults ?: false,
//     failBuildOnNewSeverity: stepParams.failBuildOnNewSeverity ?: '',
//     filterPattern: stepParams.filterPattern,
//     fullScanCycle: stepParams.fullScanCycle ?: 10,
//     fullScanScheduled: true,
//     generatePdfReport: stepParams.generatePdfReport ?: true,
//     generateXmlReport: stepParams.generateXmlReport ?: false,
//     hideDebugLogs: stepParams.hideDebugLogs ?: false,
//     highThreshold: stepParams.highThreshold ?: 1,
//     includeOpenSourceFolders: stepParams.includeOpenSourceFolders ?: '',
//     incremental: stepParams.incremental ?: false,
//     isProxy: true,
//     jobStatusOnError: 'FAILURE', // Options: GLOBAL, FAILURE, UNSTABLE
//     lowThreshold: stepParams.lowThreshold,
//     mediumThreshold: stepParams.mediumThreshold,
//     //Unimplemented
//     //osaArchiveIncludePatterns: stepParams.osaArchiveIncludePatterns ?: '*.zip, *.war, *.ear, *.tgz',
//     //osaEnabled: true,
//     //osaHighThreshold: stepParams.osaHighThreshold ?: 1,
//     //osaInstallBeforeScan: stepParams.osaInstallBeforeScan ?: false,
//     //osaLowThreshold: stepParams.osaLowThreshold,
//     //osaMediumThreshold: stepParams.osaMediumThreshold,
//     password: stepParams.password ?: '',
//     preset: stepParams.preset ?: '36',
//     sastCredentialsId: stepParams.sastCredentialsId ?: '',
//     sastEnabled: true,
//     serverUrl: stepParams.serverUrl ?: 'https://checkmarx.loyalty.com',
//     sourceEncoding: stepParams.sourceEncoding ?: '1', //utf-8
//     teamPath: stepParams.teamPath ?: '',
//     thisBuildIncremental: false,
//     thresholdSettings: stepParams.thresholdSettings ?: 'job', //'global'
//     useOwnServerCredentials: false,
//     username: stepParams.username ?: '',
//     vulnerabilityThresholdEnabled: stepParams.vulnerabilityThresholdEnabled ?: true,
//     waitForResultsEnabled: true,
//   ])
// }






