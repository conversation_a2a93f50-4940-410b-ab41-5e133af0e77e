loadLibrary("xtk:shared/nl.js");
NL.require('/nl/core/shared/xml.js');

function nms_offer_insertUpdateOffers(offer) {
// var domDocument= DOMDocument.fromXMLString(offer.toXMLString());
    
    try {

// 
// offer code
// 
        var domDocument = DOMDocument.fromXMLString(offer.toXMLString());
        var offerSNode = offerNode = "";
        var offerSNode = domDocument.getElementsByTagName("offers");
        var offerSNodeHas = offerSNode[0].hasElement("offer");
        if (offerSNode[0].hasElement("offer")) {
            offerNode = offerSNode[0].getElements("offer");
// Create a return response
            var acResponse = <acResponse/>;
            
            for (var i = 0; i < offerNode.length; i++) {
                var j = 0;
                logInfo("===========offer XML manipulation start here ===========");
// define varaible .... null value is not allowed.
                var agilityOfferCode = "";
                
                var availability_store = availability_online = false;
                var offerIdFromXML, active, awardShortenUs, awardShortenfrCa, awardType, createdAt, createdBy, displayDate, displayPriority, endDate, imagenUs, imagefrCa, legalTextenUs, legalTextfrCa, massOffer;
                var offerType, partnerId, partnerName, programType,programPriority, publishedAt, publishedBy, qualifier, startDate, updatedAt, updatedBy;

// null value allowed
                var campaignCode = cashierInstructionenUs = cashierInstructionfrCa = descriptionenUs = descriptionfrCa = issuanceCode = partnerOfferId = productBrand = productName = promotionId = "";
                var qualifierShortenUs = qualifierShortfrCa = promotionLabelenUS = promotionLabelfrCa = "";

// offer category and label.. can be null
                var offerCategory1 = offerCategory2 = offerCategory3 = offerCategory1LabelenUs = offerCategory1LabelfrCa = offerCategory2LabelenUs = offerCategory2LabelfrCa = offerCategory3LabelenUs = offerCategory3LabelfrCa = "";

// regions values
                var regions_BC = regions_AB = regions_SK = regions_MB = regions_ON = regions_QC = regions_NB = regions_PE = regions_NS = regions_NL = regions_YT = regions_NT = regions_NU = regions_TB = false;
// fetch offer elements attributes
                offerIdFromXML = offerNode[i].getAttribute("id");
                active = offerNode[i].getAttribute("active");
                agilityOfferCode = offerNode[i].getAttribute("agilityOfferCode");

// fetch availibility
                var availabilityTemp = offerNode[i].getAttribute("availability");
                availabilityTemp = availabilityTemp.replace("[", '').replace("]", '');
                var availabilitySplit = availabilityTemp.split(",");
                
                for (var temp in availabilitySplit) {
                    if (availabilitySplit[temp].trim().toLowerCase() == "instore") {
                        availability_store = true;
                    } else if (availabilitySplit[temp].trim().toLowerCase() == "online") {
                        availability_online = true;
                    }
                }
// Fetch awardShort values
                if (offerNode[i].hasElement("awardShort")) {
                    var awardShort = offerNode[i].getElements("awardShort");
                    if (awardShort[j].hasAttribute("enUs")) {
                        awardShortenUs = awardShort[j].getAttribute("enUs");
                    }
                    if (awardShort[j].hasAttribute("frCa")) {
                        awardShortenfrCa = awardShort[j].getAttribute("frCa");
                    }
                }
// Fetch Type values
                awardType = offerNode[i].getAttribute("awardType");
// null value allowed ..campaign code
                if (offerNode[i].hasAttribute("campaignCode")) {
                    logInfo("testing the node and it has the offerCategory3");
                    campaignCode = offerNode[i].getAttribute("campaignCode");
                }
// Fetch cashierInstruction
                if (offerNode[i].hasElement("cashierInstruction")) {
                    var cashierInstructionTemp = offerNode[i].getElements("cashierInstruction");
                    if (cashierInstructionTemp[j].hasAttribute("enUs")) {
                        cashierInstructionenUs = cashierInstructionTemp[j].getAttribute("enUs");
                    }
                    if (cashierInstructionTemp[j].hasAttribute("frCa")) {
                        cashierInstructionfrCa = cashierInstructionTemp[j].getAttribute("frCa");
                    }
                }

// created at and by
                createdAt = offerNode[i].getAttribute("createdAt");
                createdBy = offerNode[i].getAttribute("createdBy");
// Fetch description
                if (offerNode[i].hasElement("description")) {
                    var descriptionTemp = offerNode[i].getElements("description");
                    if (descriptionTemp[j].hasAttribute("enUs")) {
                        descriptionenUs = descriptionTemp[j].getAttribute("enUs");
                    }
                    if (descriptionTemp[j].hasAttribute("frCa")) {
                        descriptionfrCa = descriptionTemp[j].getAttribute("frCa");
                    }
                }
// display date, display priority and end date
                displayDate = offerNode[i].getAttribute("displayDate");
// displayDate= formatDate (displayDate,"%4Y/%2M/%2D");
                displayPriority = offerNode[i].getAttribute("displayPriority");
                endDate = offerNode[i].getAttribute("endDate");
// Fetch image values
                if (offerNode[i].hasElement("image")) {
                    var image = offerNode[i].getElements("image");
                    if (image[j].hasAttribute("enUs")) {
                        imagenUs = image[j].getAttribute("enUs");
                    }
                    if (image[j].hasAttribute("frCa")) {
                        imagefrCa = image[j].getAttribute("frCa");
                    }
                }

// issuancecode
                if (offerNode[i].hasAttribute("issuanceCode")) {
                    issuanceCode = offerNode[i].getAttribute("issuanceCode");
                }
// Fetch legal text
                if (offerNode[i].hasElement("legalText")) {
                    var legalText = offerNode[i].getElements("legalText");
                    if (legalText[j].hasAttribute("enUs")) {
                        legalTextenUs = legalText[j].getAttribute("enUs");
                    }
                    if (legalText[j].hasAttribute("frCa")) {
                        legalTextfrCa = legalText[j].getAttribute("frCa");
                    }
                }
// mass offer
                massOffer = offerNode[i].getAttribute("massOffer");
// offerCategory1,offerCategory2 and offerCategory3
                
                if (offerNode[i].hasAttribute("offerCategory1")) {
                    offerCategory1 = offerNode[i].getAttribute("offerCategory1");
                }
                if (offerNode[i].hasAttribute("offerCategory2")) {
                    offerCategory2 = offerNode[i].getAttribute("offerCategory2");
                }
                if (offerNode[i].hasAttribute("offerCategory3")) {
                    offerCategory3 = offerNode[i].getAttribute("offerCategory3");
                }

// fetch offerCategory1Label 1
                
                if (offerNode[i].hasElement("offerCategory1Label")) {
                    var offerCategory1Label = offerNode[i].getElements("offerCategory1Label");
                    if (offerCategory1Label[j].hasAttribute("enUs")) {
                        offerCategory1LabelenUs = offerCategory1Label[j].getAttribute("enUs");
                    }
                    if (offerCategory1Label[j].hasAttribute("frCa")) {
                        offerCategory1LabelfrCa = offerCategory1Label[j].getAttribute("frCa");
                    }
                }
// fetch offerCategory1Label2
                if (offerNode[i].hasElement("offerCategory1Labe2")) {
                    var offerCategory1Labe2 = offerNode[i].getElements("offerCategory1Labe2");
                    if (offerCategory1Labe2[j].hasAttribute("enUs")) {
                        offerCategory2LabelenUs = offerCategory1Label[j].getAttribute("enUs");
                    }
                    if (offerCategory2Label[j].hasAttribute("frCa")) {
                        offerCategory2LabelfrCa = offerCategory1Label[j].getAttribute("frCa");
                    }
                }
// fetch offerCategory1Label3
                if (offerNode[i].hasElement("offerCategory1Labe3")) {
                    var offerCategory3Label = offerNode[i].getElements("offerCategory1Labe3");
                    if (offerCategory1Labe3[j].hasAttribute("enUs")) {
                        offerCategory3LabelenUs = offerCategory1Label[j].getAttribute("enUs");
                    }
                    if (offerCategory1Labe3[j].hasAttribute("frCa")) {
                        offerCategory3LabelfrCa = offerCategory1Label[j].getAttribute("frCa");
                    }
                }

// not null values such as offertype, partnerID and partnerName
                offerType = offerNode[i].getAttribute("offerType");
                partnerId = offerNode[i].getAttribute("partnerId");
                partnerName = offerNode[i].getAttribute("partnerName");

// partnerOfferId,productBrand and productName
                if (offerNode[i].hasAttribute("partnerOfferId")) {
                    partnerOfferId = offerNode[i].getAttribute("partnerOfferId");
                }
                if (offerNode[i].hasAttribute("productBrand")) {
                    productBrand = offerNode[i].getAttribute("productBrand");
                }
                if (offerNode[i].hasAttribute("productName")) {
                    productName = offerNode[i].getAttribute("productName");
                }

                // program Type
                programType = offerNode[i].getAttribute("programType");
// program priority
                programPriority = offerNode[i].getAttribute("programPriority");
// promotionId
                promotionId = offerNode[i].getAttribute("promotionId");
// promotion label
                if (offerNode[i].hasElement("promotionLabel")) {
                    var promotionLabelTemp = offerNode[i].getElements("promotionLabel");
                    if (promotionLabelTemp[j].hasAttribute("enUs")) {
                        promotionLabelenUS = promotionLabelTemp[j].getAttribute("enUs");
                    }
                    if (promotionLabelTemp[j].hasAttribute("frCa")) {
                        promotionLabelfrCa = promotionLabelTemp[j].getAttribute("frCa");
                    }
                }
// published at, by  and qualifier
                publishedAt = offerNode[i].getAttribute("publishedAt");
                publishedBy = offerNode[i].getAttribute("publishedBy");
                qualifier = offerNode[i].getAttribute("qualifier");

// Fetch qualifierShort
                if (offerNode[i].hasElement("qualifierShort")) {
                    var qualifierShort = offerNode[i].getElements("qualifierShort");
                    if (qualifierShort[j].hasAttribute("enUs")) {
                        qualifierShortenUs = qualifierShort[j].getAttribute("enUs");
                    }
                    if (qualifierShort[j].hasAttribute("frCa")) {
                        qualifierShortfrCa = qualifierShort[j].getAttribute("frCa");
                    }
                }
// fetch start date
                startDate = offerNode[i].getAttribute("startDate");
// 
// regions availibility BC, TB, ON, MB, AB, SK
// 
                var regionsTemp = offerNode[i].getAttribute("regions");
                regionsTemp = regionsTemp.replace("[", '').replace("]", '');
                var regionsSplit = regionsTemp.split(",");
                
                for (var m = 0; m < regionsSplit.length; m++) {
                    if (regionsSplit[m].trim().toLowerCase() == "bc") {
                        regions_BC = true;
                    } else if (regionsSplit[m].trim().toLowerCase() == "nl") {
                        regions_NL = true;
                    } else if (regionsSplit[m].trim().toLowerCase() == "yt") {
                        regions_YT = true;
                    } else if (regionsSplit[m].trim().toLowerCase() == "tb") {
                        regions_TB = true;
                    } else if (regionsSplit[m].trim().toLowerCase() == "on") {
                        regions_ON = true;
                    } else if (regionsSplit[m].trim().toLowerCase() == "pe") {
                        regions_PE = true;
                    } else if (regionsSplit[m].trim().toLowerCase() == "sk") {
                        regions_SK = true;
                    } else if (regionsSplit[m].trim().toLowerCase() == "nb") {
                        regions_NB = true;
                    } else if (regionsSplit[m].trim().toLowerCase() == "ns") {
                        regions_NS = true;
                    } else if (regionsSplit[m].trim().toLowerCase() == "nu") {
                        regions_NU = true;
                    } else if (regionsSplit[m].trim().toLowerCase() == "mb") {
                        regions_MB = true;
                    } else if (regionsSplit[m].trim().toLowerCase() == "nt") {
                        regions_NT = true;
                    } else if (regionsSplit[m].trim().toLowerCase() == "ab") {
                        regions_AB = true;
                    } else if (regionsSplit[m].trim().toLowerCase() == "qc") {
                        regions_QC = true;
                    }
                }

// update at and by
                updatedAt = offerNode[i].getAttribute("updatedAt");
                updatedBy = offerNode[i].getAttribute("updatedBy");

// Get offer id
                var query = NLWS.xtkQueryDef.create({
                    queryDef: {
                        schema: "nms:offer", operation: "getIfExists",
                        select: {
                            node: [{expr: "@offerId"}, {expr: "@name"}]
                        },
                        where: {
                            condition: {expr: "@offerId = '" + offerIdFromXML + "'"}
                        }
                    }
                });
                var result = query.ExecuteQuery();
                var offerId = result.$offerId;
                var offerName = result.$name;
// insert or update?
                var insertOrUpdate;
                if (offerId == offerIdFromXML) {
                    insertOrUpdate = "update";
// logInfo("i am inside if block, value is update");
                } else {
                    insertOrUpdate = "insert";
                    offerName = 'L1_Offer_' + xtk.session.GetNewIds(1).toString();
// logInfo("i am inside else block, value is insert");
                }
                
                logInfo("value of " + insertOrUpdate);
                var insertOrUpdateOffer = insertOrUpdate;

// 
// Build offer entity
// 
                
                var offerEntity = {
                    offer: {
                        isModel: "0",
                        label: offerIdFromXML,
                        name: offerName,
                        offerId: offerIdFromXML,
                        active: active,
                        availability_store: availability_store,
                        availability_online: availability_online,
                        awardShort_enUs: awardShortenUs,
                        awardShort_frCa: awardShortenfrCa,
                        awardType: awardType,
                        campaignCode: campaignCode,
                        cashierInstruction_enUs: cashierInstructionenUs,
                        cashierInstruction_frCa: cashierInstructionfrCa,
                        createdAt: createdAt,
                        createdby: createdBy,
                        description_enUs: descriptionenUs,
                        description_frCa: descriptionfrCa,
                        displayDate: displayDate,
                        displayPriority: displayPriority,
                        endDate: endDate,
                        image_enUs: imagenUs,
                        image_frCa: imagefrCa,
                        issuanceCode: issuanceCode,
                        legalText_frCa: legalTextfrCa,
                        legalText_enUs: legalTextenUs,
                        massOffer: massOffer,
                        offerCategory1: offerCategory1,
                        offerCategory1Label_enUs: offerCategory1LabelenUs,
                        offerCategory1Label_frCa: offerCategory1LabelfrCa,
                        offerCategory2: offerCategory2,
                        offerCategory1Labe2_enUs: offerCategory2LabelenUs,
                        offerCategory1Labe2_frCa: offerCategory2LabelfrCa,
                        offerCategory3: offerCategory3,
                        offerCategory1Labe3_enUs: offerCategory3LabelenUs,
                        offerCategory1Labe3_frCa: offerCategory3LabelfrCa,
                        offerType: offerType,
                        partnerId: partnerId,
                        partnerName: partnerName,
                        partnerOfferId: partnerOfferId,
                        productBrand: productBrand,
                        productName: productName,
                        programType: programType,
                        programPriority: programPriority,
                        promotionId: promotionId,
                        promotionLabel_enUs: promotionLabelenUS,
                        promotionLabel_frCa: promotionLabelfrCa,
                        publishedAt: publishedAt,
                        publishedBy: publishedBy,
                        qualifier: qualifier,
                        qualifierShort_enUs: qualifierShortenUs,
                        qualifierShort_frCa: qualifierShortfrCa,
                        startDate: startDate,
                        regions_BC: regions_BC,
                        regions_NL: regions_NL,
                        regions_YT: regions_YT,
                        regions_TB: regions_TB,
                        regions_ON: regions_ON,
                        regions_PE: regions_PE,
                        regions_SK: regions_SK,
                        regions_NB: regions_NB,
                        regions_NS: regions_NS,
                        regions_NU: regions_NU,
                        regions_MB: regions_MB,
                        regions_NT: regions_NT,
                        regions_AB: regions_AB,
                        regions_QC: regions_QC,
                        agilityOfferCode: agilityOfferCode,
                        updatedAt: updatedAt,
                        updatedBy: updatedBy,
                        xtkschema: "nms:offer",
                        _operation: insertOrUpdate,
                        _key: "@offerId",
                        eligibilityValidation: {
                            validation: {
                                delay: "259200",
                                type: "0"
                            }
                        },
                        contentValidation: {
                            validation: {
                                delay: "259200",
                                type: "0"
                            }
                        },
                        filter: {
                            schema: "nms:recipient",
                            targetSchema: "nms:recipient"
                        },
                        forecast: {
                            simuResponseType: "0"
                        }
                        
                    }
                };
                
                logInfo(i + ".json values are" + JSON.stringify(offerEntity));
                xtk.session.Write(offerEntity);

// 
// Mechanism type code
// 
                var mechanismSNode = mechanismNode = "";
                mechanismSNode = offerNode[i].getElements("mechanisms");
                mechanismNode = mechanismSNode[0].getElements("mechanism");
                if (mechanismSNode[0].hasElement("mechanism")) {
                    for (var n = 0; n < mechanismNode.length; n++) {
                        var mechanismType = mechanismLabel_enUs = mechanismLabel_frCa = mechanismTitle_enUs = mechanismTitle_frCa = mechanismText_enUs = mechanismText_frCa = mechanismValue_enUs = mechanismValue_frCa = "";
// logInfo("mechanism block is"+mechanismNode[0].toXMLString());
                        var j = 0;
// fetch mechanismType
                        mechanismType = mechanismNode[n].getAttribute("mechanismType");
                        logInfo("====Mechanism Block code=========");

// fetch mechanismLable
                        if (mechanismNode[n].hasElement("mechanismLabel")) {
                            var mechanismLabelTemp = mechanismNode[n].getElements("mechanismLabel");
                            if (mechanismLabelTemp[j].hasAttribute("enUs")) {
                                mechanismLabel_enUs = mechanismLabelTemp[j].getAttribute("enUs");
// logInfo("inside here english lable"+mechanismLabel_enUs);
                            }
                            if (mechanismLabelTemp[j].hasAttribute("frCa")) {
                                mechanismLabel_frCa = mechanismLabelTemp[j].getAttribute("frCa");
// logInfo("inside here french label"+mechanismLabel_frCa);
                            }
                        }
// fetch mechanismTitle
                        if (mechanismNode[n].hasElement("mechanismTitle")) {
                            var mechanismTitleTemp = mechanismNode[n].getElements("mechanismTitle");
                            if (mechanismTitleTemp[j].hasAttribute("enUs")) {
                                mechanismTitle_enUs = mechanismTitleTemp[j].getAttribute("enUs");
                            }
                            if (mechanismTitleTemp[j].hasAttribute("frCa")) {
                                mechanismTitle_frCa = mechanismTitleTemp[j].getAttribute("frCa");
                            }
                        }
// fetch mechanismtext
                        if (mechanismNode[n].hasElement("mechanismText")) {
                            var mechanismTextTemp = mechanismNode[n].getElements("mechanismText");
                            if (mechanismTextTemp[j].hasAttribute("enUs")) {
                                mechanismText_enUs = mechanismTextTemp[j].getAttribute("enUs");
                            }
                            if (mechanismTextTemp[j].hasAttribute("frCa")) {
                                mechanismText_enUs = mechanismTextTemp[j].getAttribute("frCa");
                                
                            }
                        }
// fetch mechanismLable
                        if (mechanismNode[n].hasElement("mechanismValue")) {
                            var mechanismValueTemp = mechanismNode[n].getElements("mechanismValue");
                            if (mechanismValueTemp[j].hasAttribute("enUs")) {
                                mechanismValue_enUs = mechanismValueTemp[j].getAttribute("enUs");
                            }
                            if (mechanismValueTemp[j].hasAttribute("frCa")) {
                                mechanismValue_frCa = mechanismValueTemp[j].getAttribute("frCa");
                            }
                        }
// fetching mechinsm end here
// Get offer id
                        var query = NLWS.xtkQueryDef.create({
                            queryDef: {
                                schema: "L1:offerMechanism", operation: "getIfExists",
                                select: {
                                    node: [{expr: "@offerID"}, {expr: "@mechanismType"}]
                                },
                                where: {
                                    condition: [{expr: "@offerID = '" + offerIdFromXML + "'"}, {expr: "@mechanismType = '" + mechanismType + "'"}]
                                }
                            }
                        });
                        var result = query.ExecuteQuery();
                        var MechanismType_fromSchema = result.$mechanismType;
                        var OfferID_fromMechanismSchema = result.$offerID;
                        var insertOrUpdate;
                        logInfo("******** debugging the code*************");
                        logInfo("value of mechanismType from the xml:" + mechanismType);
                        logInfo("value of mechanismType from the db :" + MechanismType_fromSchema);
                        logInfo("value of id from the xml:" + id);
                        logInfo("value of id from the db:" + OfferID_fromMechanismSchema);
                        if (OfferID_fromMechanismSchema == offerIdFromXML && MechanismType_fromSchema == mechanismType) {
                            insertOrUpdate = "update";
                            logInfo("i am inside if block of the mechanism type- update");
                        } else {
                            insertOrUpdate = "insert";
                            logInfo("i am inside else block of the mechanism type- insert");
                            
                        }
                        
                        var offerMechanismEntity = {
                            offerMechanism: {
                                offerID: offerIdFromXML,
                                mechanismType: mechanismType,
                                mechanismLabel_enUs: mechanismLabel_enUs,
                                mechanismLabel_frCa: mechanismLabel_frCa,
                                mechanismTitle_enUs: mechanismTitle_enUs,
                                mechanismTitle_frCa: mechanismTitle_frCa,
                                mechanismText_enUs: mechanismText_enUs,
                                mechanismText_frCa: mechanismText_frCa,
                                mechanismValue_enUs: mechanismValue_enUs,
                                mechanismValue_frCa: mechanismValue_frCa,
                                xtkschema: "L1:offerMechanism",
                                _operation: insertOrUpdate,
                                _key: "@offerID,@mechanismType"
                            }
                        };
                        logInfo(n + "offer mechanism xml typw" + JSON.stringify(offerMechanismEntity));
                        xtk.session.Write(offerMechanismEntity);
                        
                    }
// for loop closure
                
                }
// if block closure
// code ends here for mechanism


//
// Tiers type code
// 
                var tierSNode = tierNode = "";
                var awardValue = qualifierValue = qualifierFrequency = awardLongenUs = awardLongfrCa = qualifierLongEnUs = qualifierLongfrCa = tierID = "";
                var contents = contentTierID = contetnID = contentLabel_enUs = contentLabel_frCa = contentLabel = contentProductSKU = contentUpc = "";
                var contentMasterProduct = false;
                
                tierSNode = offerNode[i].getElements("tiers");
                var tierHas = tierSNode[0].hasElement("tier");
                logInfo("this has tier?-  " + tierHas);
                logInfo("=================tier block starts here==============");
                if (tierSNode[0].hasElement("tier")) {
                    tierNode = tierSNode[0].getElements("tier");
                    for (var p = 0; p < tierNode.length; p++) {
                        var j = 0;
// fetch the awardValue and Qualifier value
                        if (tierNode[p].hasAttribute("awardValue")) {
                            awardValue = tierNode[p].getAttribute("awardValue");
                        }
                        if (tierNode[p].hasAttribute("qualifierValue")) {
                            qualifierValue = tierNode[p].getAttribute("qualifierValue");
                        }
                        if (tierNode[p].hasAttribute("qualifierFrequency")) {
                            qualifierFrequency = tierNode[p].getAttribute("qualifierFrequency");
                        }
                        if (tierNode[p].hasAttribute("tierID")) {
                            tierID = tierNode[p].getAttribute("tierID");
                        }

// fetch AwardLong
                        if (tierNode[p].hasElement("awardLong")) {
                            var awardLongTemp = tierNode[p].getElements("awardLong");
                            if (awardLongTemp[j].hasAttribute("enUs")) {
                                awardLongenUs = awardLongTemp[j].getAttribute("enUs");
                            }
                            if (awardLongTemp[j].hasAttribute("frCa")) {
                                awardLongfrCa = awardLongTemp[j].getAttribute("frCa");
                            }
                        }
// fetch qualifierLong
                        if (tierNode[p].hasElement("qualifierLong")) {
                            var qualifierLongTemp = tierNode[p].getElements("qualifierLong");
                            if (qualifierLongTemp[j].hasAttribute("enUs")) {
                                qualifierLongEnUs = qualifierLongTemp[j].getAttribute("enUs");
                            }
                            if (qualifierLongTemp[j].hasAttribute("frCa")) {
                                qualifierLongfrCa = qualifierLongTemp[j].getAttribute("frCa");
                            }
                        }
// 
// Content insertion
// 

// get contenst of the tiesrs
                        logInfo("// content blocks start here// ");
                        
                        if (tierNode[p].hasElement("content")) {
                            contentNode = tierNode[p].getElements("content");
                            for (var k = 0; k < contentNode.length; k++) {
// contents=contentNode[k].getElements("content");
                                if (contentNode[k].hasAttribute("tierID")) {
                                    contentTierID = contentNode[k].getAttribute("tierID");
                                }
                                if (contentNode[k].hasAttribute("label_enUs")) {
                                    contentLabel_enUs = contentNode[k].getAttribute("label_enUs");
                                }
                                if (contentNode[k].hasAttribute("label_frCa")) {
                                    contentLabel_frCa = contentNode[k].getAttribute("label_frCa");
                                }
                                if (contentNode[k].hasAttribute("productSKU")) {
                                    contentProductSKU = contentNode[k].getAttribute("productSKU");
                                }
                                if (contentNode[k].hasAttribute("upc")) {
                                    contentUpc = contentNode[k].getAttribute("upc");
                                }
                                if (contentNode[k].hasAttribute("masterProduct")) {
                                    contentMasterProduct = contentNode[k].getAttribute("masterProduct");
                                }

// Get offerContentID
                                var query = NLWS.xtkQueryDef.create({
                                    queryDef: {
                                        schema: "L1:offerTierContent", operation: "getIfExists",
                                        select: {
                                            node: [{expr: "@tierID"}, {expr: "@id"}]
                                        },
                                        where: {
                                            condition: [{expr: "@tierID = '" + contentTierID + "'"}, {expr: "@offerID = '" + offerIdFromXML + "'"}, {expr: "@content_productSKU = '" + contentProductSKU + "'"}, {expr: "@content_upc = '" + contentUpc + "'"}, {expr: "@content_masterProduct = " + contentMasterProduct + ""}]
                                        }
                                    }
                                });
                                var result = query.ExecuteQuery();
// var offerID_tierContentSchema = result.$offerID;
                                var tierID_tierContentSchema = result.$tierID;
                                var pkId_tierContentSchema = result.$id;
// var contetnID_tierContentSchema=result.$contetnID;
                                var insertOrUpdate;
                                logInfo("******** debugging the code*************");
                                logInfo("value of contentTierID from the xml{contertTier}:" + contentTierID);
                                logInfo("value of contentTierID from the db :" + tierID_tierContentSchema);


// logInfo("value of primarykey from the xml{contertTier}:"+id);
                                logInfo("value of primarykey from the db :" + pkId_tierContentSchema);
                                logInfo("value of primarykey from the db :" + typeof pkId_tierContentSchema);
                                
                                if (pkId_tierContentSchema != '') {
                                    insertOrUpdate = "update";
                                    logInfo("i am inside if block to check update");
                                } else {
                                    insertOrUpdate = "insert";
                                    pkId_tierContentSchema = xtk.session.GetNewIds(1);
                                    logInfo("autopk generated " + pkId_tierContentSchema);
                                    
                                }
                                
                                
                                var id = pkId_tierContentSchema;
                                
                                var offerTierContentEntity = {
                                    offerTierContent: {
                                        offerID: offerIdFromXML,
                                        tierID: contentTierID,
                                        id: id,
                                        contentLabel_enUS: contentLabel_enUs,
                                        contentLabel_frCa: contentLabel_frCa,
                                        content_productSKU: contentProductSKU,
                                        content_upc: contentUpc,
                                        content_masterProduct: contentMasterProduct,
                                        xtkschema: "L1:offerTierContent",
                                        _operation: insertOrUpdate,
                                        _key: "@id"
                                    }
                                };
                                
                                logInfo(k + ".json values for the offer tier contes are" + JSON.stringify(offerTierContentEntity));
                                xtk.session.Write(offerTierContentEntity);
                                
                            }
// for loop close
                        
                        
                        } // if block gets closed here

// 
// content code ends here
// 

// Get offer id
                        var query = NLWS.xtkQueryDef.create({
                            queryDef: {
                                schema: "L1:offerTier", operation: "getIfExists",
                                select: {
                                    node: [{expr: "@offerID"}, {expr: "@tierID"}]
                                },
                                where: {
                                    condition: [{expr: "@offerID = '" + offerIdFromXML + "'"}, {expr: "@tierID = '" + tierID + "'"}]
                                }
                            }
                        });
                        var result = query.ExecuteQuery();
                        var offerIDfromSchema = result.$offerID;
                        var tierIDValuefromSchema = result.$tierID;
                        var insertOrUpdate;
                        if (offerIDfromSchema == offerIdFromXML && tierIDValuefromSchema == tierID) {
                            insertOrUpdate = "update";
                            logInfo("i am inside if block to check update");
                        } else {
                            insertOrUpdate = "insert";
                            
                        }
                        
                        var offerTierEntity = {
                            offerTier: {
                                offerID: offerIdFromXML,
                                tierID: tierID,
                                awardValue: awardValue,
                                qualifierValue: qualifierValue,
                                qualifierFrequency: qualifierFrequency,
                                awardLong_enUS: awardLongenUs,
                                awardLong_frCa: awardLongfrCa,
                                qualifierLong_enUS: qualifierLongEnUs,
                                qualifierLong_frCa: qualifierLongfrCa,
                                xtkschema: "L1:offerTier",
                                _operation: insertOrUpdate,
                                _key: "@tierID"
                            }
                        };
                        logInfo(i + ".json values  for the tier table are" + JSON.stringify(offerTierEntity));
                        xtk.session.Write(offerTierEntity);
                        
                        
                    }
// for loop end here
                }
// tier code ends here

// Final offerStag ends here
                acResponse.appendChild(<offer offerId={offerIdFromXML.toString()} offerName={offerName} insertOrUpdate={insertOrUpdateOffer}/>);
            }
// offer for loop ends here
        
        }
// offer ifblock code ends here
    } catch (e) {
        return " throw an Error" + e;
    }
// acResponse.appendChild(<offer offerId={offerId.toString()} offerName={offerName}   insertOrUpdate={insertOrUpdate}  />);
    return acResponse
}

// closing the main function loop
