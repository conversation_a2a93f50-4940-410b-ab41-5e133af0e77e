{"Environment": "prod", "StackPrefix": "nova-acc-offer-consumer", "OfferEventBusStackPrefix": "nova-event-bus-offer", "KMSKeyAppName": "amrpwl-prod-application", "VpcId": "vpc-09aa1ade9bdba1446", "SubnetId1": "subnet-092f9ce6b31f81c23", "SubnetId2": "subnet-0cb159065c3c9c5c2", "SubnetId3": "subnet-081851a3d01c55b39", "KinesisForSplunkStackName": "prod-kinesissplunk", "PagerDutyURLOfferConsumer": "https://events.pagerduty.com/integration/d4837a241f3b410cd042d5fe84dc7fa4/enqueue", "ErroralarmThresholdaccConsumer": "1", "DurationalarmThresholdaccConsumer": "210000", "ThrottlealarmThresholdaccConsumer": "1", "IteratoragealarmThresholdaccConsumer": "600000"}