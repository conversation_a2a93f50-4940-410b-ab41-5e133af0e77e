AWSTemplateFormatVersion: 2010-09-09
Description: Template to create secrets manager for Acc Project
Parameters:
  StackPrefix:
    Type: String
    Default: nova-acc-offer-consumer
    Description: Name of CloudFormation stack prefix
  Environment:
    Type: String
    Description: Environment Name
    Default: dev
Resources:
  AccCredentialsSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub '${Environment}-${StackPrefix}-credentials'
      Description: Credentials for Acc Project
      SecretString: '
      {
        "username": "USERNAME_PLACEHOLDER",
        "password": "PASSWORD_PLACEHOLDER",
        "endpointURL": "ENDPOINT_PLACEHOLDER"
      }
      '
  AccTokenSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub '${Environment}-${StackPrefix}-token'
      Description: Token for Acc Project
      SecretString: '
      {
        "sessionToken": "SESSION_TOKEN_PLACEHOLDER",
        "securityToken": "SECURITY_TOKEN_PLACEHOLDER"
      }
      '
Outputs:
  AccCredentialsSecret:
    Description: Name of Secret for Acc Project
    Value: !Ref AccCredentialsSecret
    Export:
      Name: !Sub '${Environment}-${StackPrefix}-credentials'
  AccTokenSecret:
    Description: Name of Secret for Acc Project
    Value: !Ref AccTokenSecret
    Export:
      Name: !Sub '${Environment}-${StackPrefix}-token'
