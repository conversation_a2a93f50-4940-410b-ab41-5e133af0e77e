AWSTemplateFormatVersion: '2010-09-09'
Transform: 'AWS::Serverless-2016-10-31'
Description: Template to sync offers with Adobe Campaign Code
Parameters:
  ErroralarmThresholdaccConsumer:
    Type: Number
    Description: Checks if the lambda invocation encountered an error
    Default: 1
  DurationalarmThresholdaccConsumer:
    Type: Number
    Description: Checks the duration of lambda invocation
    Default: 600000
  ThrottlealarmThresholdaccConsumer:
    Type: Number
    Description: The percentage threshold at which a throttle alarm is triggered
    Default: 1
  IteratoragealarmThresholdaccConsumer:
    Type: Number
    Description: IteratorAge for lambdas which consume from Kinesis
    Default: 600000
  StackPrefix:
    Type: String
    Default: nova-acc-offer-consumer
    Description: Name of CloudFormation stack prefix
  Environment:
    Type: String
    Description: Environment Name
    Default: dev
  OfferEventBusStackPrefix:
    Type: String
    Description: Stack Prefix for Offer Published EventBus
  PagerDutyURLOfferConsumer:
    Type: String
    Description: Pager Duty endpoint
  VpcId:
    Type: String
    Description: Vpc id
  SubnetId1:
    Type: String
    Description: Subnet 1
    Default: none
  SubnetId2:
    Type: String
    Description: Subnet 2
    Default: none
  SubnetId3:
    Type: String
    Description: Subnet 3
    Default: none
  KMSKeyAppName:
    Description: The name of the KMS Key
    Type: String
    Default: amrpwl-nonprod-application
  KinesisForSplunkStackName:
    Description: The name of Kinesis which is unique for each environment, used for logging to splunk
    Type: String
Conditions:
  SoleEnv:
    Fn::Equals:
      - Ref: Environment
      - sole
  NotSoleEnv:
    "Fn::Not":
      - Condition: SoleEnv
  IsDevEnvironment:
    Fn::Equals:
      - Ref: Environment
      - 'dev'
Mappings:
  AssignMemory:
    sole:
      memory: 3008
    dev:
      memory: 3008
    uat:
      memory: 3008
    prod:
      memory: 3008
  AssignedReservedConcurrency:
    sole:
      reserved: 1
    dev:
      reserved: 1
    uat:
      reserved: 1
    prod:
      reserved: 3

Resources:
  AccOfferConsumerLambda:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName:
        Fn::Sub: '${AWS::StackName}'
      Description: Lambda to sync offers with Adobe Campaign Code consumer via SOAP
      Tracing: PassThrough
      CodeUri: PLACEHOLDER
      Handler: org.springframework.cloud.function.adapter.aws.FunctionInvoker::handleRequest
      Runtime: java11
      MemorySize:
        Fn::FindInMap:
          - AssignMemory
          - Ref: Environment
          - "memory"
      Timeout: 300
      ReservedConcurrentExecutions:
        Fn::FindInMap:
          - AssignedReservedConcurrency
          - Ref: Environment
          - "reserved"
      Role:
        Fn::GetAtt: [ LambdaPermissions, Arn ]
      VpcConfig:
        SecurityGroupIds:
          - Ref: LambdaSecurityGroup
        SubnetIds:
          - Ref: SubnetId1
          - Ref: SubnetId2
          - Ref: SubnetId3
      Environment:
        Variables:
          AWS_SECRET_CREDENTIALS:
            Fn::Sub: '${Environment}-${StackPrefix}-credentials'
          AWS_SECRET_TOKEN:
            Fn::Sub: '${Environment}-${StackPrefix}-token'
          SPRING_PROFILES_ACTIVE:
            Ref: Environment
          JAVA_TOOL_OPTIONS: '-XX:TieredStopAtLevel=1 -Xverify:none -Dspring.jmx.enabled=false -Dspring.cloud.function.definition=offerPublishedEventFunction'

  LambdaTrigger:
    Type: AWS::Lambda::EventSourceMapping
    DependsOn:
      - AccOfferConsumerLambda
      - KinesisPolicy
    Condition: NotSoleEnv
    Properties:
      EventSourceArn:
        Fn::ImportValue:
          Fn::Sub: '${Environment}-${OfferEventBusStackPrefix}-Kinesis-ARN'
      FunctionName:
        Ref: AccOfferConsumerLambda
      StartingPosition: TRIM_HORIZON
      BatchSize: 100

  LambdaSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Allow http to client host
      VpcId:
        Ref: VpcId
      SecurityGroupEgress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
        - IpProtocol: tcp
          FromPort: 8080
          ToPort: 8080
          CidrIp: 0.0.0.0/0

  KinesisPolicy:
    Type: AWS::IAM::Policy
    DependsOn: LambdaPermissions
    Condition: NotSoleEnv
    Properties:
      PolicyName:
        Fn::Sub: '${AWS::StackName}-StreamPermissions'
      Roles:
        - Ref: LambdaPermissions
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: 'Allow'
            Action:
              - kinesis:DescribeStream
              - kinesis:GetRecords
              - kinesis:GetShardIterator
              - kinesis:ListShards
              - kinesis:ListStreams
            Resource:
              Fn::ImportValue:
                Fn::Sub: '${Environment}-${OfferEventBusStackPrefix}-Kinesis-ARN'

  LambdaPermissions:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: 'Allow'
            Principal:
              Service:
                - 'lambda.amazonaws.com'
            Action:
              - 'sts:AssumeRole'
      RoleName:
        Fn::Sub: '${AWS::StackName}-permissions'
      Policies:
        - PolicyName:
            Fn::Sub: '${AWS::StackName}-ec2'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 'ec2:CreateNetworkInterface'
                  - 'ec2:DescribeNetworkInterfaces'
                  - 'ec2:DeleteNetworkInterface'
                Resource:
                  - '*'
        - PolicyName:
            Fn::Sub: '${AWS::StackName}-secretsmanager'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 'secretsmanager:GetSecretValue'
                Resource:
                  - Fn::ImportValue:
                      Fn::Sub: '${Environment}-${StackPrefix}-credentials'
                  - Fn::ImportValue:
                      Fn::Sub: '${Environment}-${StackPrefix}-token'
              - Effect: Allow
                Action:
                  - 'secretsmanager:UpdateSecret'
                Resource:
                  - Fn::ImportValue:
                      Fn::Sub: '${Environment}-${StackPrefix}-token'
        - PolicyName:
            Fn::Sub: '${AWS::StackName}-Logs'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Action:
                  - logs:PutLogEvents
                  - logs:CreateLogStream
                  - logs:CreateLogGroup
                Resource:
                  Fn::GetAtt: [ ConsumerLogGroup, Arn ]
        - PolicyName:
            Fn::Sub: '${AWS::StackName}-KMS'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Resource:
                  - Fn::ImportValue:
                      Ref:
                        KMSKeyAppName
                Action:
                  - kms:Decrypt
  ConsumerLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName:
        Fn::Sub: '/aws/lambda/${AWS::StackName}'
      RetentionInDays: 14
  SubscriptionFilter:
    DependsOn: ConsumerLogGroup
    Type: 'AWS::Logs::SubscriptionFilter'
    Properties:
      RoleArn:
        Fn::ImportValue:
          Fn::Sub: '${KinesisForSplunkStackName}-Role-Arn'
      LogGroupName:
        Ref: ConsumerLogGroup
      FilterPattern: ''
      DestinationArn:
        Fn::ImportValue:
          Fn::Sub: '${KinesisForSplunkStackName}-Stream-Arn'
  AlarmSNSTopic:
    Type: AWS::SNS::Topic
    Properties:
      Subscription:
        - Endpoint:
            Fn::Sub: '${PagerDutyURLOfferConsumer}'
          Protocol: https
      TopicName:
        Fn::Sub: '${AWS::StackName}-SNS-Alarm'
  ErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: Checks if the lambda invocation encountered an error
      AlarmActions:
        - Ref: 'AlarmSNSTopic'
      OKActions:
        - Ref: 'AlarmSNSTopic'
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Maximum
      Period: '60'
      EvaluationPeriods: '1'
      Threshold:
        Ref: ErroralarmThresholdaccConsumer
      ActionsEnabled:
        Fn::If:
          - IsDevEnvironment
          - false
          - true
      TreatMissingData: ignore
      ComparisonOperator: GreaterThanOrEqualToThreshold
      Dimensions:
        - Name: FunctionName
          Value:
            Ref: AccOfferConsumerLambda
  DurationAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: Checks the duration of lambda invocation
      AlarmActions:
        - Ref: 'AlarmSNSTopic'
      OKActions:
        - Ref: 'AlarmSNSTopic'
      MetricName: Duration
      Namespace: AWS/Lambda
      Statistic: Maximum
      Period: '60'
      EvaluationPeriods: '1'
      Threshold:
        Ref: DurationalarmThresholdaccConsumer
      ActionsEnabled:
        Fn::If:
          - IsDevEnvironment
          - false
          - true
      TreatMissingData: ignore
      ComparisonOperator: GreaterThanOrEqualToThreshold
      Dimensions:
        - Name: FunctionName
          Value:
            Ref: AccOfferConsumerLambda
  ThrottleAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: "Checks if the Lambda function is being throttled"
      AlarmActions:
        - Ref: 'AlarmSNSTopic'
      OKActions:
        - Ref: 'AlarmSNSTopic'
      MetricName: Throttles
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: '60'
      EvaluationPeriods: '1'
      Threshold:
        Ref: ThrottlealarmThresholdaccConsumer
      ActionsEnabled:
        Fn::If:
          - IsDevEnvironment
          - false
          - true
      TreatMissingData: ignore
      ComparisonOperator: GreaterThanOrEqualToThreshold
      Dimensions:
        - Name: FunctionName
          Value:
            Ref: AccOfferConsumerLambda
  IteratorAgeAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: "IteratorAge for lambdas which consume from Kinesis"
      AlarmActions:
        - Ref: 'AlarmSNSTopic'
      OKActions:
        - Ref: 'AlarmSNSTopic'
      MetricName: IteratorAge
      Namespace: AWS/Lambda
      Statistic: Maximum
      Period: '60'
      EvaluationPeriods: '1'
      Threshold:
        Ref: IteratoragealarmThresholdaccConsumer
      ActionsEnabled:
        Fn::If:
          - IsDevEnvironment
          - false
          - true
      TreatMissingData: ignore
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: FunctionName
          Value:
            Ref: AccOfferConsumerLambda
Outputs:
  ConsumerLogGroup:
    Description: The name of the log group created for the app
    Value:
      Ref: ConsumerLogGroup
    Export:
      Name:
        Fn::Sub: '${AWS::StackName}-ConsumerLogGroupName'
  AccOfferConsumerLambda:
    Description: Lambda function
    Value:
      Ref: AccOfferConsumerLambda
    Export:
      Name:
        Fn::Sub: '${AWS::StackName}-Lambda'
  AccOfferConsumerLambdaArn:
    Description: Get Offer Consumer Lambda function Arn
    Value:
      Fn::GetAtt: [ AccOfferConsumerLambda, Arn ]
    Export:
      Name:
        Fn::Sub: '${AWS::StackName}-ARN'