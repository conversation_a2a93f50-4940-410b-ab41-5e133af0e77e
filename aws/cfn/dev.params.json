{"Environment": "dev", "StackPrefix": "nova-acc-offer-consumer", "OfferEventBusStackPrefix": "nova-event-bus-offer", "KMSKeyAppName": "amrpwl-nonprod-application", "VpcId": "vpc-033de5f52e0ff993b", "SubnetId1": "subnet-02d9fa1e617ed1833", "SubnetId2": "subnet-034ca364a5500f16b", "SubnetId3": "subnet-03533bbf2381016c9", "KinesisForSplunkStackName": "nonprod-kinesissplunk", "PagerDutyURLOfferConsumer": "https://events.pagerduty.com/integration/d132264d93f747baa79ead1a770f1935/enqueue", "ErroralarmThresholdaccConsumer": "1", "DurationalarmThresholdaccConsumer": "210000", "ThrottlealarmThresholdaccConsumer": "1", "IteratoragealarmThresholdaccConsumer": "600000"}