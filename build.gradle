plugins {
    id "io.spring.dependency-management" apply false
    id "net.researchgate.release"
    id "org.jetbrains.kotlin.jvm" apply false
    id "org.jetbrains.kotlin.plugin.jpa" apply false
    id "org.jetbrains.kotlin.plugin.spring" apply false
    id "org.springframework.boot" apply false
}

apply from: "dependency.gradle"

if (!project.hasProperty("profiles")) {
    ext.profiles = "local"
}

buildScan {
    termsOfServiceUrl = "https://gradle.com/terms-of-service"
    termsOfServiceAgree = "yes"
}

configurations {
    detekt
}

dependencies {
    detekt "io.gitlab.arturbosch.detekt:detekt-cli"
}
//
//jacocoTestReport {
//    reports {
//        xml.enabled true
//    }
//}

task detekt(type: JavaExec) {
    main = "io.gitlab.arturbosch.detekt.cli.Main"
    classpath = configurations.detekt
    def input = "$rootDir"
    def config = "detekt.yml"
    def exclude = ".*/resources/.*,.*/build/.*"
    def report = "html:${project.buildDir}/reports/detekt.html"
    def params = ["-i", input, "-c", config, "-ex", exclude, "-r", report]
    args(params)
}

def jfrogUser = System.getProperty('jfrogUser') != null ? System.getProperty('jfrogUser') : "localUser"
def jfrogPassword = System.getProperty('jfrogPassword') != null ? System.getProperty('jfrogPassword') : "localPassword"


allprojects {
    group = "com.loyalty.nova.acc.offer"
    version = "${version}"

    apply plugin: "kotlin"
    apply plugin: "io.spring.dependency-management"
    apply plugin: "jacoco"

    sourceCompatibility = 11

    targetCompatibility = 11

    compileKotlin {
        kotlinOptions {
            freeCompilerArgs = ["-Xjsr305=strict"]
            jvmTarget = JavaVersion.VERSION_11.toString()
        }
    }

    compileTestKotlin {
        kotlinOptions {
            freeCompilerArgs = ["-Xjsr305=strict"]
            jvmTarget = JavaVersion.VERSION_11.toString()
        }
    }

    repositories {
        repositories {
            mavenCentral()
            gradlePluginPortal()
            google()
            jcenter()
            maven { url "https://dl.bintray.com/kotlin/kotlinx.html/" }
            maven { url "https://repo.spring.io/milestone" }
            maven { url "https://s3-us-west-2.amazonaws.com/dynamodb-local/release" }
            maven {
                url "https://loyalty.jfrog.io/loyalty/libs-release"
                credentials {
                    username = "${jfrogUser}"
                    password = "${jfrogPassword}"
                }
            }
            maven {
                url "https://loyalty.jfrog.io/loyalty/libs-snapshot"
                credentials {
                    username = "${jfrogUser}"
                    password = "${jfrogPassword}"
                }
            }
        }
    }

    dependencies {
        testImplementation(
                "org.hamcrest:java-hamcrest",
                "org.jetbrains.kotlin:kotlin-test",
                "org.junit.jupiter:junit-jupiter",
                "org.mockito:mockito-core",
                "org.mockito:mockito-junit-jupiter",
                "org.springframework.boot:spring-boot-starter-test",
        )

        testRuntimeOnly(
                "com.almworks.sqlite4java:sqlite4java"
        )
    }

    dependencyManagement {
        dependencies {
            libraries.each {
                library -> dependency library.value
            }
        }
        imports {
            mavenBom "io.projectreactor:reactor-bom:${versions.reactorBomVersion}"
            mavenBom "org.springframework.boot:spring-boot-starter-parent:${versions.springBootVersion}"
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${versions.springCloudVersion}"
            mavenBom "org.springframework.cloud:spring-cloud-stream-dependencies:${versions.springCloudStreamVersion}"
        }
    }

    jacoco {
        toolVersion = "0.8.7"
        reportsDir = file("$buildDir/reports")
    }

    jacocoTestReport {
        reports {
            xml.enabled = true
            xml.setDestination(file("${buildDir}/reports/jacoco/report.xml"))
            html.enabled = true
            html.setDestination(file("${buildDir}/reports/jacoco/"))
        }

        // what to exclude from coverage report
        // UI, "noise", generated classes, platform classes, etc.
        def excludes = [
                '**/R.class',
                '**/R$*.class',
                '**/*$ViewInjector*.*',
                '**/BuildConfig.*',
                '**/Manifest*.*',
                '**/*Test*.*',
                '**/*Fragment.*',
                '**/*Activity.*'
        ]
        getClassDirectories().setFrom(fileTree(
                dir: "$buildDir/classes/kotlin/main"
        ))
        def coverageSourceDirs = [
                "src/main/kotlin"
        ]
        getAdditionalSourceDirs().setFrom(files(coverageSourceDirs))
        getSourceDirectories().setFrom(files(coverageSourceDirs))
        getExecutionData().setFrom(files("$buildDir/jacoco/test.exec"))
    }

    task allDependencies(type: DependencyReportTask) {}

    test {
        useJUnitPlatform {
            includeEngines "junit-jupiter"
            excludeEngines "junit-vintage"
        }
        // Enable to debug unit tests
        testLogging.showStandardStreams = false
        jvmArgs "-Dspring.profiles.active=test"
    }

    release {
        failOnSnapshotDependencies = true
        revertOnFail = true
        git {
            requireBranch = "master"
        }
    }

    check.dependsOn detekt
}